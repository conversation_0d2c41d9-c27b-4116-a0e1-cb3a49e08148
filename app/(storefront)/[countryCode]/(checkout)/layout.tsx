import React from "react"
import LocalizedClientLink from "@/features/storefront/modules/common/components/localized-client-link"
import ChevronDown from "@/features/storefront/modules/common/icons/chevron-down"
import OpenfrontCTA from "@/features/storefront/modules/layout/components/openfront-cta"

export default function CheckoutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="w-full bg-background relative">
      <div className="h-16 bg-background border-b ">
        <nav className="flex h-full items-center max-w-[1440px] w-full mx-auto px-6 justify-between">
          <LocalizedClientLink
            href="/cart"
            className="text-xs font-semibold text-foreground flex items-center gap-x-2 uppercase flex-1 basis-0"
            data-testid="back-to-cart-link"
          >
            <ChevronDown className="rotate-90" size={16} />
            <span className="mt-px hidden sm:block text-xs font-medium leading-5 text-muted-foreground hover:text-foreground ">
              Back to shopping cart
            </span>
            <span className="mt-px block sm:hidden text-xs font-medium leading-5 text-muted-foreground hover:text-foreground">
              Back
            </span>
          </LocalizedClientLink>
          <LocalizedClientLink
            href="/"
            className="text-lg font-medium leading-5 text-muted-foreground hover:text-foreground uppercase"
            data-testid="store-link"
          >
            Openfront Store
          </LocalizedClientLink>
          <div className="flex-1 basis-0" />
        </nav>
      </div>
      <div className="relative" data-testid="checkout-container">{children}</div>
      <div className="py-4 w-full flex items-center justify-center">
        <OpenfrontCTA />
      </div>
    </div>
  )
}
