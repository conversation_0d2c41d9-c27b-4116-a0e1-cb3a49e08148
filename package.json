{"name": "openfront", "version": "1.0.0", "private": true, "license": "MIT", "scripts": {"dev": "keystone build --no-ui && npm run migrate && next dev --turbopack", "build": "keystone build --no-ui && npm run migrate && next build", "start": "next start", "lint": "next lint", "migrate:gen": "keystone build --no-ui && keystone prisma migrate dev", "migrate": "keystone prisma migrate deploy", "test": "vitest", "test-e2e": "playwright test"}, "dependencies": {"@apollo/client": "^3.13.8", "@envelop/core": "^5.2.3", "@escape.tech/graphql-armor": "^3.1.2", "@headlessui/react": "^2.2.1", "@hookform/resolvers": "^3.9.1", "@keystone-6/auth": "^8.1.0", "@keystone-6/core": "^6.3.1", "@keystone-6/document-renderer": "^1.1.2", "@keystone-6/fields-document": "^9.1.1", "@medusajs/icons": "^2.6.1", "@medusajs/ui": "^4.0.7", "@paypal/react-paypal-js": "^8.8.3", "@preconstruct/next": "^4.0.0", "@prisma/client": "6.5.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.2", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "^1.1.8", "@react-three/fiber": "^9.1.2", "@remixicon/react": "^4.6.0", "@stripe/react-stripe-js": "^3.6.0", "@tailwindcss/postcss": "^4.0.12", "@tanstack/react-query": "^5.77.0", "@tanstack/react-query-devtools": "^5.77.0", "@tanstack/react-query-next-experimental": "^5.77.0", "@tanstack/react-table": "^8.21.2", "@types/three": "^0.176.0", "autoprefixer": "^10.4.20", "bytes": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "embla-carousel-react": "8.5.1", "fast-deep-equal": "^3.1.3", "framer-motion": "^12.12.2", "glob": "^10.3.10", "graphql": "^16.10.0", "graphql-middleware": "^6.1.35", "graphql-request": "^5.0.0", "graphql-upload": "^15.0.2", "graphql-yoga": "^3.1.0", "input-otp": "1.4.1", "lru-cache": "^11.1.0", "lucide-react": "^0.454.0", "next": "^15.3.1", "next-themes": "latest", "nodemailer": "^6.10.0", "pluralize": "^8.0.0", "rate-limiter-flexible": "^6.1.0", "react": "^19.0.0", "react-country-flag": "^3.1.0", "react-day-picker": "^9.6.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "react-select": "^5.10.1", "recharts": "2.15.0", "rimraf": "^5.0.5", "slugify": "^1.6.6", "sonner": "^1.7.1", "stripe": "^17.7.0", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "tailwindcss": "latest", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0", "vaul": "1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@medusajs/types": "^2.6.1", "@mswjs/data": "^0.16.1", "@playwright/test": "^1.43.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.5.2", "@types/graphql-upload": "^17.0.0", "@types/lodash": "^4.17.16", "@types/node": "^20.14.10", "@types/nodemailer": "^6.4.17", "@types/pluralize": "^0.0.33", "@types/react": "latest", "@types/react-dom": "latest", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^9.24.0", "eslint-config-next": "^15.2.4", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-playwright": "^1.6.0", "eslint-plugin-testing-library": "^6.2.2", "eslint-plugin-vitest": "^0.5.4", "jsdom": "^24.0.0", "msw": "^2.2.14", "plop": "^4.0.1", "prisma": "6.5.0", "typescript": "^5.5.0", "vite-tsconfig-paths": "^4.3.2", "vitest": "^2.1.4"}, "overrides": {"graphql": "^16.10.0", "next": "^15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "glob": "^10.3.10", "rimraf": "^5.0.5"}, "msw": {"workerDirectory": "public"}}