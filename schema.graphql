# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type Address {
  id: ID!
  label: String
  company: String
  firstName: String
  lastName: String
  address1: String
  address2: String
  city: String
  province: String
  postalCode: String
  phone: String
  isBilling: Boolean
  metadata: JSON
  country: Country
  user: User
  shippingProviders(where: ShippingProviderWhereInput! = {}, orderBy: [ShippingProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingProviderWhereUniqueInput): [ShippingProvider!]
  shippingProvidersCount(where: ShippingProviderWhereInput! = {}): Int
  cart: Cart
  claimOrders(where: ClaimOrderWhereInput! = {}, orderBy: [ClaimOrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimOrderWhereUniqueInput): [ClaimOrder!]
  claimOrdersCount(where: ClaimOrderWhereInput! = {}): Int
  ordersUsingAsBillingAddress(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersUsingAsBillingAddressCount(where: OrderWhereInput! = {}): Int
  ordersUsingAsShippingAddress(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersUsingAsShippingAddressCount(where: OrderWhereInput! = {}): Int
  cartsUsingAsBillingAddress(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsUsingAsBillingAddressCount(where: CartWhereInput! = {}): Int
  cartsUsingAsShippingAddress(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsUsingAsShippingAddressCount(where: CartWhereInput! = {}): Int
  swaps(where: SwapWhereInput! = {}, orderBy: [SwapOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SwapWhereUniqueInput): [Swap!]
  swapsCount(where: SwapWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input AddressWhereUniqueInput {
  id: ID
}

input AddressWhereInput {
  AND: [AddressWhereInput!]
  OR: [AddressWhereInput!]
  NOT: [AddressWhereInput!]
  id: IDFilter
  company: StringFilter
  firstName: StringFilter
  lastName: StringFilter
  address1: StringFilter
  address2: StringFilter
  city: StringFilter
  province: StringFilter
  postalCode: StringFilter
  phone: StringFilter
  isBilling: BooleanFilter
  country: CountryWhereInput
  user: UserWhereInput
  shippingProviders: ShippingProviderManyRelationFilter
  cart: CartWhereInput
  claimOrders: ClaimOrderManyRelationFilter
  ordersUsingAsBillingAddress: OrderManyRelationFilter
  ordersUsingAsShippingAddress: OrderManyRelationFilter
  cartsUsingAsBillingAddress: CartManyRelationFilter
  cartsUsingAsShippingAddress: CartManyRelationFilter
  swaps: SwapManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: NestedStringFilter
}

enum QueryMode {
  default
  insensitive
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input BooleanFilter {
  equals: Boolean
  not: BooleanFilter
}

input ShippingProviderManyRelationFilter {
  every: ShippingProviderWhereInput
  some: ShippingProviderWhereInput
  none: ShippingProviderWhereInput
}

input ClaimOrderManyRelationFilter {
  every: ClaimOrderWhereInput
  some: ClaimOrderWhereInput
  none: ClaimOrderWhereInput
}

input OrderManyRelationFilter {
  every: OrderWhereInput
  some: OrderWhereInput
  none: OrderWhereInput
}

input CartManyRelationFilter {
  every: CartWhereInput
  some: CartWhereInput
  none: CartWhereInput
}

input SwapManyRelationFilter {
  every: SwapWhereInput
  some: SwapWhereInput
  none: SwapWhereInput
}

input DateTimeFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeFilter
}

input AddressOrderByInput {
  id: OrderDirection
  company: OrderDirection
  firstName: OrderDirection
  lastName: OrderDirection
  address1: OrderDirection
  address2: OrderDirection
  city: OrderDirection
  province: OrderDirection
  postalCode: OrderDirection
  phone: OrderDirection
  isBilling: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input AddressUpdateInput {
  company: String
  firstName: String
  lastName: String
  address1: String
  address2: String
  city: String
  province: String
  postalCode: String
  phone: String
  isBilling: Boolean
  metadata: JSON
  country: CountryRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  shippingProviders: ShippingProviderRelateToManyForUpdateInput
  cart: CartRelateToOneForUpdateInput
  claimOrders: ClaimOrderRelateToManyForUpdateInput
  ordersUsingAsBillingAddress: OrderRelateToManyForUpdateInput
  ordersUsingAsShippingAddress: OrderRelateToManyForUpdateInput
  cartsUsingAsBillingAddress: CartRelateToManyForUpdateInput
  cartsUsingAsShippingAddress: CartRelateToManyForUpdateInput
  swaps: SwapRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryRelateToOneForUpdateInput {
  create: CountryCreateInput
  connect: CountryWhereUniqueInput
  disconnect: Boolean
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input ShippingProviderRelateToManyForUpdateInput {
  disconnect: [ShippingProviderWhereUniqueInput!]
  set: [ShippingProviderWhereUniqueInput!]
  create: [ShippingProviderCreateInput!]
  connect: [ShippingProviderWhereUniqueInput!]
}

input CartRelateToOneForUpdateInput {
  create: CartCreateInput
  connect: CartWhereUniqueInput
  disconnect: Boolean
}

input ClaimOrderRelateToManyForUpdateInput {
  disconnect: [ClaimOrderWhereUniqueInput!]
  set: [ClaimOrderWhereUniqueInput!]
  create: [ClaimOrderCreateInput!]
  connect: [ClaimOrderWhereUniqueInput!]
}

input OrderRelateToManyForUpdateInput {
  disconnect: [OrderWhereUniqueInput!]
  set: [OrderWhereUniqueInput!]
  create: [OrderCreateInput!]
  connect: [OrderWhereUniqueInput!]
}

input CartRelateToManyForUpdateInput {
  disconnect: [CartWhereUniqueInput!]
  set: [CartWhereUniqueInput!]
  create: [CartCreateInput!]
  connect: [CartWhereUniqueInput!]
}

input SwapRelateToManyForUpdateInput {
  disconnect: [SwapWhereUniqueInput!]
  set: [SwapWhereUniqueInput!]
  create: [SwapCreateInput!]
  connect: [SwapWhereUniqueInput!]
}

input AddressUpdateArgs {
  where: AddressWhereUniqueInput!
  data: AddressUpdateInput!
}

input AddressCreateInput {
  company: String
  firstName: String
  lastName: String
  address1: String
  address2: String
  city: String
  province: String
  postalCode: String
  phone: String
  isBilling: Boolean
  metadata: JSON
  country: CountryRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  shippingProviders: ShippingProviderRelateToManyForCreateInput
  cart: CartRelateToOneForCreateInput
  claimOrders: ClaimOrderRelateToManyForCreateInput
  ordersUsingAsBillingAddress: OrderRelateToManyForCreateInput
  ordersUsingAsShippingAddress: OrderRelateToManyForCreateInput
  cartsUsingAsBillingAddress: CartRelateToManyForCreateInput
  cartsUsingAsShippingAddress: CartRelateToManyForCreateInput
  swaps: SwapRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryRelateToOneForCreateInput {
  create: CountryCreateInput
  connect: CountryWhereUniqueInput
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

input ShippingProviderRelateToManyForCreateInput {
  create: [ShippingProviderCreateInput!]
  connect: [ShippingProviderWhereUniqueInput!]
}

input CartRelateToOneForCreateInput {
  create: CartCreateInput
  connect: CartWhereUniqueInput
}

input ClaimOrderRelateToManyForCreateInput {
  create: [ClaimOrderCreateInput!]
  connect: [ClaimOrderWhereUniqueInput!]
}

input OrderRelateToManyForCreateInput {
  create: [OrderCreateInput!]
  connect: [OrderWhereUniqueInput!]
}

input CartRelateToManyForCreateInput {
  create: [CartCreateInput!]
  connect: [CartWhereUniqueInput!]
}

input SwapRelateToManyForCreateInput {
  create: [SwapCreateInput!]
  connect: [SwapWhereUniqueInput!]
}

type ApiKey {
  id: ID!
  user: User
  createdAt: DateTime
  updatedAt: DateTime
}

input ApiKeyWhereUniqueInput {
  id: ID
}

input ApiKeyWhereInput {
  AND: [ApiKeyWhereInput!]
  OR: [ApiKeyWhereInput!]
  NOT: [ApiKeyWhereInput!]
  id: IDFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ApiKeyOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ApiKeyUpdateInput {
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ApiKeyUpdateArgs {
  where: ApiKeyWhereUniqueInput!
  data: ApiKeyUpdateInput!
}

input ApiKeyCreateInput {
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type BatchJob {
  id: ID!
  type: BatchJobTypeType
  status: BatchJobStatusType
  context: JSON
  result: JSON
  error: String
  progress: Int
  createdBy: User
  completedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

enum BatchJobTypeType {
  PRODUCT_IMPORT
  ORDER_EXPORT
  INVENTORY_UPDATE
  PRICE_UPDATE
}

enum BatchJobStatusType {
  CREATED
  PROCESSING
  COMPLETED
  FAILED
  CANCELED
}

input BatchJobWhereUniqueInput {
  id: ID
}

input BatchJobWhereInput {
  AND: [BatchJobWhereInput!]
  OR: [BatchJobWhereInput!]
  NOT: [BatchJobWhereInput!]
  id: IDFilter
  type: BatchJobTypeTypeNullableFilter
  status: BatchJobStatusTypeNullableFilter
  error: StringFilter
  progress: IntNullableFilter
  createdBy: UserWhereInput
  completedAt: DateTimeNullableFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input BatchJobTypeTypeNullableFilter {
  equals: BatchJobTypeType
  in: [BatchJobTypeType!]
  notIn: [BatchJobTypeType!]
  not: BatchJobTypeTypeNullableFilter
}

input BatchJobStatusTypeNullableFilter {
  equals: BatchJobStatusType
  in: [BatchJobStatusType!]
  notIn: [BatchJobStatusType!]
  not: BatchJobStatusTypeNullableFilter
}

input IntNullableFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntNullableFilter
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input BatchJobOrderByInput {
  id: OrderDirection
  type: OrderDirection
  status: OrderDirection
  error: OrderDirection
  progress: OrderDirection
  completedAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input BatchJobUpdateInput {
  type: BatchJobTypeType
  status: BatchJobStatusType
  context: JSON
  result: JSON
  error: String
  progress: Int
  createdBy: UserRelateToOneForUpdateInput
  completedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input BatchJobUpdateArgs {
  where: BatchJobWhereUniqueInput!
  data: BatchJobUpdateInput!
}

input BatchJobCreateInput {
  type: BatchJobTypeType
  status: BatchJobStatusType
  context: JSON
  result: JSON
  error: String
  progress: Int
  createdBy: UserRelateToOneForCreateInput
  completedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

type Capture {
  id: ID!
  amount: Int
  payment: Payment
  metadata: JSON
  createdBy: String
  createdAt: DateTime
  updatedAt: DateTime
}

input CaptureWhereUniqueInput {
  id: ID
}

input CaptureWhereInput {
  AND: [CaptureWhereInput!]
  OR: [CaptureWhereInput!]
  NOT: [CaptureWhereInput!]
  id: IDFilter
  amount: IntFilter
  payment: PaymentWhereInput
  createdBy: StringFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input IntFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntFilter
}

input CaptureOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  createdBy: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CaptureUpdateInput {
  amount: Int
  payment: PaymentRelateToOneForUpdateInput
  metadata: JSON
  createdBy: String
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentRelateToOneForUpdateInput {
  create: PaymentCreateInput
  connect: PaymentWhereUniqueInput
  disconnect: Boolean
}

input CaptureUpdateArgs {
  where: CaptureWhereUniqueInput!
  data: CaptureUpdateInput!
}

input CaptureCreateInput {
  amount: Int
  payment: PaymentRelateToOneForCreateInput
  metadata: JSON
  createdBy: String
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentRelateToOneForCreateInput {
  create: PaymentCreateInput
  connect: PaymentWhereUniqueInput
}

type Cart {
  id: ID!
  email: String
  type: CartTypeType
  metadata: JSON
  idempotencyKey: String
  context: JSON
  paymentAuthorizedAt: DateTime
  abandonedEmailSent: Boolean
  user: User
  region: Region
  addresses(where: AddressWhereInput! = {}, orderBy: [AddressOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: AddressWhereUniqueInput): [Address!]
  addressesCount(where: AddressWhereInput! = {}): Int
  discounts(where: DiscountWhereInput! = {}, orderBy: [DiscountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountWhereUniqueInput): [Discount!]
  discountsCount(where: DiscountWhereInput! = {}): Int
  giftCards(where: GiftCardWhereInput! = {}, orderBy: [GiftCardOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardWhereUniqueInput): [GiftCard!]
  giftCardsCount(where: GiftCardWhereInput! = {}): Int
  draftOrder: DraftOrder
  order: Order
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  customShippingOptions(where: CustomShippingOptionWhereInput! = {}, orderBy: [CustomShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomShippingOptionWhereUniqueInput): [CustomShippingOption!]
  customShippingOptionsCount(where: CustomShippingOptionWhereInput! = {}): Int
  swap: Swap
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  payment: Payment
  paymentCollection: PaymentCollection
  billingAddress: Address
  shippingAddress: Address
  abandonedFor: Int
  status: CartStatus
  isActive: Boolean
  subtotal: String
  total: String
  rawTotal: Int
  rawSubtotal: String
  rawTotalBreakdown: String
  discount: String
  giftCardTotal: String
  tax: String
  shipping: String
  cheapestShipping: String
  discountsById: JSON
  checkoutStep: String
  createdAt: DateTime
  updatedAt: DateTime
}

enum CartTypeType {
  default
  swap
  draft_order
  payment_link
  claim
}

enum CartStatus {
  ACTIVE
  COMPLETED
}

input CartWhereUniqueInput {
  id: ID
  draftOrder: DraftOrderWhereUniqueInput
  order: OrderWhereUniqueInput
  swap: SwapWhereUniqueInput
  payment: PaymentWhereUniqueInput
  paymentCollection: PaymentCollectionWhereUniqueInput
}

input CartWhereInput {
  AND: [CartWhereInput!]
  OR: [CartWhereInput!]
  NOT: [CartWhereInput!]
  id: IDFilter
  email: StringFilter
  type: CartTypeTypeNullableFilter
  idempotencyKey: StringFilter
  paymentAuthorizedAt: DateTimeNullableFilter
  abandonedEmailSent: BooleanFilter
  user: UserWhereInput
  region: RegionWhereInput
  addresses: AddressManyRelationFilter
  discounts: DiscountManyRelationFilter
  giftCards: GiftCardManyRelationFilter
  draftOrder: DraftOrderWhereInput
  order: OrderWhereInput
  lineItems: LineItemManyRelationFilter
  customShippingOptions: CustomShippingOptionManyRelationFilter
  swap: SwapWhereInput
  shippingMethods: ShippingMethodManyRelationFilter
  payment: PaymentWhereInput
  paymentCollection: PaymentCollectionWhereInput
  billingAddress: AddressWhereInput
  shippingAddress: AddressWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CartTypeTypeNullableFilter {
  equals: CartTypeType
  in: [CartTypeType!]
  notIn: [CartTypeType!]
  not: CartTypeTypeNullableFilter
}

input AddressManyRelationFilter {
  every: AddressWhereInput
  some: AddressWhereInput
  none: AddressWhereInput
}

input DiscountManyRelationFilter {
  every: DiscountWhereInput
  some: DiscountWhereInput
  none: DiscountWhereInput
}

input GiftCardManyRelationFilter {
  every: GiftCardWhereInput
  some: GiftCardWhereInput
  none: GiftCardWhereInput
}

input LineItemManyRelationFilter {
  every: LineItemWhereInput
  some: LineItemWhereInput
  none: LineItemWhereInput
}

input CustomShippingOptionManyRelationFilter {
  every: CustomShippingOptionWhereInput
  some: CustomShippingOptionWhereInput
  none: CustomShippingOptionWhereInput
}

input ShippingMethodManyRelationFilter {
  every: ShippingMethodWhereInput
  some: ShippingMethodWhereInput
  none: ShippingMethodWhereInput
}

input CartOrderByInput {
  id: OrderDirection
  email: OrderDirection
  type: OrderDirection
  idempotencyKey: OrderDirection
  paymentAuthorizedAt: OrderDirection
  abandonedEmailSent: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CartUpdateInput {
  email: String
  type: CartTypeType
  metadata: JSON
  idempotencyKey: String
  context: JSON
  paymentAuthorizedAt: DateTime
  abandonedEmailSent: Boolean
  user: UserRelateToOneForUpdateInput
  region: RegionRelateToOneForUpdateInput
  addresses: AddressRelateToManyForUpdateInput
  discounts: DiscountRelateToManyForUpdateInput
  giftCards: GiftCardRelateToManyForUpdateInput
  draftOrder: DraftOrderRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  customShippingOptions: CustomShippingOptionRelateToManyForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  shippingMethods: ShippingMethodRelateToManyForUpdateInput
  payment: PaymentRelateToOneForUpdateInput
  paymentCollection: PaymentCollectionRelateToOneForUpdateInput
  billingAddress: AddressRelateToOneForUpdateInput
  shippingAddress: AddressRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RegionRelateToOneForUpdateInput {
  create: RegionCreateInput
  connect: RegionWhereUniqueInput
  disconnect: Boolean
}

input AddressRelateToManyForUpdateInput {
  disconnect: [AddressWhereUniqueInput!]
  set: [AddressWhereUniqueInput!]
  create: [AddressCreateInput!]
  connect: [AddressWhereUniqueInput!]
}

input DiscountRelateToManyForUpdateInput {
  disconnect: [DiscountWhereUniqueInput!]
  set: [DiscountWhereUniqueInput!]
  create: [DiscountCreateInput!]
  connect: [DiscountWhereUniqueInput!]
}

input GiftCardRelateToManyForUpdateInput {
  disconnect: [GiftCardWhereUniqueInput!]
  set: [GiftCardWhereUniqueInput!]
  create: [GiftCardCreateInput!]
  connect: [GiftCardWhereUniqueInput!]
}

input DraftOrderRelateToOneForUpdateInput {
  create: DraftOrderCreateInput
  connect: DraftOrderWhereUniqueInput
  disconnect: Boolean
}

input OrderRelateToOneForUpdateInput {
  create: OrderCreateInput
  connect: OrderWhereUniqueInput
  disconnect: Boolean
}

input LineItemRelateToManyForUpdateInput {
  disconnect: [LineItemWhereUniqueInput!]
  set: [LineItemWhereUniqueInput!]
  create: [LineItemCreateInput!]
  connect: [LineItemWhereUniqueInput!]
}

input CustomShippingOptionRelateToManyForUpdateInput {
  disconnect: [CustomShippingOptionWhereUniqueInput!]
  set: [CustomShippingOptionWhereUniqueInput!]
  create: [CustomShippingOptionCreateInput!]
  connect: [CustomShippingOptionWhereUniqueInput!]
}

input SwapRelateToOneForUpdateInput {
  create: SwapCreateInput
  connect: SwapWhereUniqueInput
  disconnect: Boolean
}

input ShippingMethodRelateToManyForUpdateInput {
  disconnect: [ShippingMethodWhereUniqueInput!]
  set: [ShippingMethodWhereUniqueInput!]
  create: [ShippingMethodCreateInput!]
  connect: [ShippingMethodWhereUniqueInput!]
}

input PaymentCollectionRelateToOneForUpdateInput {
  create: PaymentCollectionCreateInput
  connect: PaymentCollectionWhereUniqueInput
  disconnect: Boolean
}

input AddressRelateToOneForUpdateInput {
  create: AddressCreateInput
  connect: AddressWhereUniqueInput
  disconnect: Boolean
}

input CartUpdateArgs {
  where: CartWhereUniqueInput!
  data: CartUpdateInput!
}

input CartCreateInput {
  email: String
  type: CartTypeType
  metadata: JSON
  idempotencyKey: String
  context: JSON
  paymentAuthorizedAt: DateTime
  abandonedEmailSent: Boolean
  user: UserRelateToOneForCreateInput
  region: RegionRelateToOneForCreateInput
  addresses: AddressRelateToManyForCreateInput
  discounts: DiscountRelateToManyForCreateInput
  giftCards: GiftCardRelateToManyForCreateInput
  draftOrder: DraftOrderRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  customShippingOptions: CustomShippingOptionRelateToManyForCreateInput
  swap: SwapRelateToOneForCreateInput
  shippingMethods: ShippingMethodRelateToManyForCreateInput
  payment: PaymentRelateToOneForCreateInput
  paymentCollection: PaymentCollectionRelateToOneForCreateInput
  billingAddress: AddressRelateToOneForCreateInput
  shippingAddress: AddressRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RegionRelateToOneForCreateInput {
  create: RegionCreateInput
  connect: RegionWhereUniqueInput
}

input AddressRelateToManyForCreateInput {
  create: [AddressCreateInput!]
  connect: [AddressWhereUniqueInput!]
}

input DiscountRelateToManyForCreateInput {
  create: [DiscountCreateInput!]
  connect: [DiscountWhereUniqueInput!]
}

input GiftCardRelateToManyForCreateInput {
  create: [GiftCardCreateInput!]
  connect: [GiftCardWhereUniqueInput!]
}

input DraftOrderRelateToOneForCreateInput {
  create: DraftOrderCreateInput
  connect: DraftOrderWhereUniqueInput
}

input OrderRelateToOneForCreateInput {
  create: OrderCreateInput
  connect: OrderWhereUniqueInput
}

input LineItemRelateToManyForCreateInput {
  create: [LineItemCreateInput!]
  connect: [LineItemWhereUniqueInput!]
}

input CustomShippingOptionRelateToManyForCreateInput {
  create: [CustomShippingOptionCreateInput!]
  connect: [CustomShippingOptionWhereUniqueInput!]
}

input SwapRelateToOneForCreateInput {
  create: SwapCreateInput
  connect: SwapWhereUniqueInput
}

input ShippingMethodRelateToManyForCreateInput {
  create: [ShippingMethodCreateInput!]
  connect: [ShippingMethodWhereUniqueInput!]
}

input PaymentCollectionRelateToOneForCreateInput {
  create: PaymentCollectionCreateInput
  connect: PaymentCollectionWhereUniqueInput
}

input AddressRelateToOneForCreateInput {
  create: AddressCreateInput
  connect: AddressWhereUniqueInput
}

type ClaimImage {
  id: ID!
  image: ImageFieldOutput
  altText: String
  claimItem: ClaimItem
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

type ImageFieldOutput {
  id: ID!
  filesize: Int!
  width: Int!
  height: Int!
  extension: ImageExtension!
  url: String!
}

enum ImageExtension {
  jpg
  png
  webp
  gif
}

input ClaimImageWhereUniqueInput {
  id: ID
}

input ClaimImageWhereInput {
  AND: [ClaimImageWhereInput!]
  OR: [ClaimImageWhereInput!]
  NOT: [ClaimImageWhereInput!]
  id: IDFilter
  altText: StringFilter
  claimItem: ClaimItemWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ClaimImageOrderByInput {
  id: OrderDirection
  altText: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ClaimImageUpdateInput {
  image: ImageFieldInput
  altText: String
  claimItem: ClaimItemRelateToOneForUpdateInput
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ImageFieldInput {
  upload: Upload!
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

input ClaimItemRelateToOneForUpdateInput {
  create: ClaimItemCreateInput
  connect: ClaimItemWhereUniqueInput
  disconnect: Boolean
}

input ClaimImageUpdateArgs {
  where: ClaimImageWhereUniqueInput!
  data: ClaimImageUpdateInput!
}

input ClaimImageCreateInput {
  image: ImageFieldInput
  altText: String
  claimItem: ClaimItemRelateToOneForCreateInput
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ClaimItemRelateToOneForCreateInput {
  create: ClaimItemCreateInput
  connect: ClaimItemWhereUniqueInput
}

type ClaimItem {
  id: ID!
  reason: ClaimItemReasonType
  note: String
  quantity: Int
  metadata: JSON
  productVariant: ProductVariant
  lineItem: LineItem
  claimOrder: ClaimOrder
  claimImages(where: ClaimImageWhereInput! = {}, orderBy: [ClaimImageOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimImageWhereUniqueInput): [ClaimImage!]
  claimImagesCount(where: ClaimImageWhereInput! = {}): Int
  claimTags(where: ClaimTagWhereInput! = {}, orderBy: [ClaimTagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimTagWhereUniqueInput): [ClaimTag!]
  claimTagsCount(where: ClaimTagWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum ClaimItemReasonType {
  missing_item
  wrong_item
  production_failure
  other
}

input ClaimItemWhereUniqueInput {
  id: ID
}

input ClaimItemWhereInput {
  AND: [ClaimItemWhereInput!]
  OR: [ClaimItemWhereInput!]
  NOT: [ClaimItemWhereInput!]
  id: IDFilter
  reason: ClaimItemReasonTypeNullableFilter
  note: StringFilter
  quantity: IntFilter
  productVariant: ProductVariantWhereInput
  lineItem: LineItemWhereInput
  claimOrder: ClaimOrderWhereInput
  claimImages: ClaimImageManyRelationFilter
  claimTags: ClaimTagManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ClaimItemReasonTypeNullableFilter {
  equals: ClaimItemReasonType
  in: [ClaimItemReasonType!]
  notIn: [ClaimItemReasonType!]
  not: ClaimItemReasonTypeNullableFilter
}

input ClaimImageManyRelationFilter {
  every: ClaimImageWhereInput
  some: ClaimImageWhereInput
  none: ClaimImageWhereInput
}

input ClaimTagManyRelationFilter {
  every: ClaimTagWhereInput
  some: ClaimTagWhereInput
  none: ClaimTagWhereInput
}

input ClaimItemOrderByInput {
  id: OrderDirection
  reason: OrderDirection
  note: OrderDirection
  quantity: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ClaimItemUpdateInput {
  reason: ClaimItemReasonType
  note: String
  quantity: Int
  metadata: JSON
  productVariant: ProductVariantRelateToOneForUpdateInput
  lineItem: LineItemRelateToOneForUpdateInput
  claimOrder: ClaimOrderRelateToOneForUpdateInput
  claimImages: ClaimImageRelateToManyForUpdateInput
  claimTags: ClaimTagRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductVariantRelateToOneForUpdateInput {
  create: ProductVariantCreateInput
  connect: ProductVariantWhereUniqueInput
  disconnect: Boolean
}

input LineItemRelateToOneForUpdateInput {
  create: LineItemCreateInput
  connect: LineItemWhereUniqueInput
  disconnect: Boolean
}

input ClaimOrderRelateToOneForUpdateInput {
  create: ClaimOrderCreateInput
  connect: ClaimOrderWhereUniqueInput
  disconnect: Boolean
}

input ClaimImageRelateToManyForUpdateInput {
  disconnect: [ClaimImageWhereUniqueInput!]
  set: [ClaimImageWhereUniqueInput!]
  create: [ClaimImageCreateInput!]
  connect: [ClaimImageWhereUniqueInput!]
}

input ClaimTagRelateToManyForUpdateInput {
  disconnect: [ClaimTagWhereUniqueInput!]
  set: [ClaimTagWhereUniqueInput!]
  create: [ClaimTagCreateInput!]
  connect: [ClaimTagWhereUniqueInput!]
}

input ClaimItemUpdateArgs {
  where: ClaimItemWhereUniqueInput!
  data: ClaimItemUpdateInput!
}

input ClaimItemCreateInput {
  reason: ClaimItemReasonType
  note: String
  quantity: Int
  metadata: JSON
  productVariant: ProductVariantRelateToOneForCreateInput
  lineItem: LineItemRelateToOneForCreateInput
  claimOrder: ClaimOrderRelateToOneForCreateInput
  claimImages: ClaimImageRelateToManyForCreateInput
  claimTags: ClaimTagRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductVariantRelateToOneForCreateInput {
  create: ProductVariantCreateInput
  connect: ProductVariantWhereUniqueInput
}

input LineItemRelateToOneForCreateInput {
  create: LineItemCreateInput
  connect: LineItemWhereUniqueInput
}

input ClaimOrderRelateToOneForCreateInput {
  create: ClaimOrderCreateInput
  connect: ClaimOrderWhereUniqueInput
}

input ClaimImageRelateToManyForCreateInput {
  create: [ClaimImageCreateInput!]
  connect: [ClaimImageWhereUniqueInput!]
}

input ClaimTagRelateToManyForCreateInput {
  create: [ClaimTagCreateInput!]
  connect: [ClaimTagWhereUniqueInput!]
}

type ClaimOrder {
  id: ID!
  paymentStatus: ClaimOrderPaymentStatusType
  fulfillmentStatus: ClaimOrderFulfillmentStatusType
  type: ClaimOrderTypeType
  refundAmount: Int
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  address: Address
  order: Order
  claimItems(where: ClaimItemWhereInput! = {}, orderBy: [ClaimItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimItemWhereUniqueInput): [ClaimItem!]
  claimItemsCount(where: ClaimItemWhereInput! = {}): Int
  fulfillments(where: FulfillmentWhereInput! = {}, orderBy: [FulfillmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentWhereUniqueInput): [Fulfillment!]
  fulfillmentsCount(where: FulfillmentWhereInput! = {}): Int
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  return: Return
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum ClaimOrderPaymentStatusType {
  na
  not_refunded
  refunded
}

enum ClaimOrderFulfillmentStatusType {
  not_fulfilled
  partially_fulfilled
  fulfilled
  partially_shipped
  shipped
  partially_returned
  returned
  canceled
  requires_action
}

enum ClaimOrderTypeType {
  refund
  replace
}

input ClaimOrderWhereUniqueInput {
  id: ID
  return: ReturnWhereUniqueInput
}

input ClaimOrderWhereInput {
  AND: [ClaimOrderWhereInput!]
  OR: [ClaimOrderWhereInput!]
  NOT: [ClaimOrderWhereInput!]
  id: IDFilter
  paymentStatus: ClaimOrderPaymentStatusTypeNullableFilter
  fulfillmentStatus: ClaimOrderFulfillmentStatusTypeNullableFilter
  type: ClaimOrderTypeTypeNullableFilter
  refundAmount: IntNullableFilter
  canceledAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotification: BooleanFilter
  address: AddressWhereInput
  order: OrderWhereInput
  claimItems: ClaimItemManyRelationFilter
  fulfillments: FulfillmentManyRelationFilter
  lineItems: LineItemManyRelationFilter
  return: ReturnWhereInput
  shippingMethods: ShippingMethodManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ClaimOrderPaymentStatusTypeNullableFilter {
  equals: ClaimOrderPaymentStatusType
  in: [ClaimOrderPaymentStatusType!]
  notIn: [ClaimOrderPaymentStatusType!]
  not: ClaimOrderPaymentStatusTypeNullableFilter
}

input ClaimOrderFulfillmentStatusTypeNullableFilter {
  equals: ClaimOrderFulfillmentStatusType
  in: [ClaimOrderFulfillmentStatusType!]
  notIn: [ClaimOrderFulfillmentStatusType!]
  not: ClaimOrderFulfillmentStatusTypeNullableFilter
}

input ClaimOrderTypeTypeNullableFilter {
  equals: ClaimOrderTypeType
  in: [ClaimOrderTypeType!]
  notIn: [ClaimOrderTypeType!]
  not: ClaimOrderTypeTypeNullableFilter
}

input ClaimItemManyRelationFilter {
  every: ClaimItemWhereInput
  some: ClaimItemWhereInput
  none: ClaimItemWhereInput
}

input FulfillmentManyRelationFilter {
  every: FulfillmentWhereInput
  some: FulfillmentWhereInput
  none: FulfillmentWhereInput
}

input ClaimOrderOrderByInput {
  id: OrderDirection
  paymentStatus: OrderDirection
  fulfillmentStatus: OrderDirection
  type: OrderDirection
  refundAmount: OrderDirection
  canceledAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotification: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ClaimOrderUpdateInput {
  paymentStatus: ClaimOrderPaymentStatusType
  fulfillmentStatus: ClaimOrderFulfillmentStatusType
  type: ClaimOrderTypeType
  refundAmount: Int
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  address: AddressRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  claimItems: ClaimItemRelateToManyForUpdateInput
  fulfillments: FulfillmentRelateToManyForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  return: ReturnRelateToOneForUpdateInput
  shippingMethods: ShippingMethodRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ClaimItemRelateToManyForUpdateInput {
  disconnect: [ClaimItemWhereUniqueInput!]
  set: [ClaimItemWhereUniqueInput!]
  create: [ClaimItemCreateInput!]
  connect: [ClaimItemWhereUniqueInput!]
}

input FulfillmentRelateToManyForUpdateInput {
  disconnect: [FulfillmentWhereUniqueInput!]
  set: [FulfillmentWhereUniqueInput!]
  create: [FulfillmentCreateInput!]
  connect: [FulfillmentWhereUniqueInput!]
}

input ReturnRelateToOneForUpdateInput {
  create: ReturnCreateInput
  connect: ReturnWhereUniqueInput
  disconnect: Boolean
}

input ClaimOrderUpdateArgs {
  where: ClaimOrderWhereUniqueInput!
  data: ClaimOrderUpdateInput!
}

input ClaimOrderCreateInput {
  paymentStatus: ClaimOrderPaymentStatusType
  fulfillmentStatus: ClaimOrderFulfillmentStatusType
  type: ClaimOrderTypeType
  refundAmount: Int
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  address: AddressRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  claimItems: ClaimItemRelateToManyForCreateInput
  fulfillments: FulfillmentRelateToManyForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  return: ReturnRelateToOneForCreateInput
  shippingMethods: ShippingMethodRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ClaimItemRelateToManyForCreateInput {
  create: [ClaimItemCreateInput!]
  connect: [ClaimItemWhereUniqueInput!]
}

input FulfillmentRelateToManyForCreateInput {
  create: [FulfillmentCreateInput!]
  connect: [FulfillmentWhereUniqueInput!]
}

input ReturnRelateToOneForCreateInput {
  create: ReturnCreateInput
  connect: ReturnWhereUniqueInput
}

type ClaimTag {
  id: ID!
  value: String
  metadata: JSON
  claimItems(where: ClaimItemWhereInput! = {}, orderBy: [ClaimItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimItemWhereUniqueInput): [ClaimItem!]
  claimItemsCount(where: ClaimItemWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ClaimTagWhereUniqueInput {
  id: ID
}

input ClaimTagWhereInput {
  AND: [ClaimTagWhereInput!]
  OR: [ClaimTagWhereInput!]
  NOT: [ClaimTagWhereInput!]
  id: IDFilter
  value: StringFilter
  claimItems: ClaimItemManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ClaimTagOrderByInput {
  id: OrderDirection
  value: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ClaimTagUpdateInput {
  value: String
  metadata: JSON
  claimItems: ClaimItemRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ClaimTagUpdateArgs {
  where: ClaimTagWhereUniqueInput!
  data: ClaimTagUpdateInput!
}

input ClaimTagCreateInput {
  value: String
  metadata: JSON
  claimItems: ClaimItemRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Country {
  id: ID!
  iso2: String
  iso3: String
  numCode: Int
  name: String
  displayName: String
  region: Region
  addresses(where: AddressWhereInput! = {}, orderBy: [AddressOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: AddressWhereUniqueInput): [Address!]
  addressesCount(where: AddressWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryWhereUniqueInput {
  id: ID
  iso2: String
}

input CountryWhereInput {
  AND: [CountryWhereInput!]
  OR: [CountryWhereInput!]
  NOT: [CountryWhereInput!]
  id: IDFilter
  iso2: StringFilter
  iso3: StringFilter
  numCode: IntFilter
  name: StringFilter
  displayName: StringFilter
  region: RegionWhereInput
  addresses: AddressManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CountryOrderByInput {
  id: OrderDirection
  iso2: OrderDirection
  iso3: OrderDirection
  numCode: OrderDirection
  name: OrderDirection
  displayName: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CountryUpdateInput {
  iso2: String
  iso3: String
  numCode: Int
  name: String
  displayName: String
  region: RegionRelateToOneForUpdateInput
  addresses: AddressRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryUpdateArgs {
  where: CountryWhereUniqueInput!
  data: CountryUpdateInput!
}

input CountryCreateInput {
  iso2: String
  iso3: String
  numCode: Int
  name: String
  displayName: String
  region: RegionRelateToOneForCreateInput
  addresses: AddressRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Currency {
  id: ID!
  code: String
  symbol: String
  symbolNative: String
  name: String
  moneyAmounts(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  moneyAmountsCount(where: MoneyAmountWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  payments(where: PaymentWhereInput! = {}, orderBy: [PaymentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentWhereUniqueInput): [Payment!]
  paymentsCount(where: PaymentWhereInput! = {}): Int
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  stores(where: StoreWhereInput! = {}, orderBy: [StoreOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: StoreWhereUniqueInput): [Store!]
  storesCount(where: StoreWhereInput! = {}): Int
  noDivisionCurrency: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

input CurrencyWhereUniqueInput {
  id: ID
  code: String
}

input CurrencyWhereInput {
  AND: [CurrencyWhereInput!]
  OR: [CurrencyWhereInput!]
  NOT: [CurrencyWhereInput!]
  id: IDFilter
  code: StringFilter
  symbol: StringFilter
  symbolNative: StringFilter
  name: StringFilter
  moneyAmounts: MoneyAmountManyRelationFilter
  orders: OrderManyRelationFilter
  payments: PaymentManyRelationFilter
  regions: RegionManyRelationFilter
  stores: StoreManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input MoneyAmountManyRelationFilter {
  every: MoneyAmountWhereInput
  some: MoneyAmountWhereInput
  none: MoneyAmountWhereInput
}

input PaymentManyRelationFilter {
  every: PaymentWhereInput
  some: PaymentWhereInput
  none: PaymentWhereInput
}

input RegionManyRelationFilter {
  every: RegionWhereInput
  some: RegionWhereInput
  none: RegionWhereInput
}

input StoreManyRelationFilter {
  every: StoreWhereInput
  some: StoreWhereInput
  none: StoreWhereInput
}

input CurrencyOrderByInput {
  id: OrderDirection
  code: OrderDirection
  symbol: OrderDirection
  symbolNative: OrderDirection
  name: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CurrencyUpdateInput {
  code: String
  symbol: String
  symbolNative: String
  name: String
  moneyAmounts: MoneyAmountRelateToManyForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  payments: PaymentRelateToManyForUpdateInput
  regions: RegionRelateToManyForUpdateInput
  stores: StoreRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input MoneyAmountRelateToManyForUpdateInput {
  disconnect: [MoneyAmountWhereUniqueInput!]
  set: [MoneyAmountWhereUniqueInput!]
  create: [MoneyAmountCreateInput!]
  connect: [MoneyAmountWhereUniqueInput!]
}

input PaymentRelateToManyForUpdateInput {
  disconnect: [PaymentWhereUniqueInput!]
  set: [PaymentWhereUniqueInput!]
  create: [PaymentCreateInput!]
  connect: [PaymentWhereUniqueInput!]
}

input RegionRelateToManyForUpdateInput {
  disconnect: [RegionWhereUniqueInput!]
  set: [RegionWhereUniqueInput!]
  create: [RegionCreateInput!]
  connect: [RegionWhereUniqueInput!]
}

input StoreRelateToManyForUpdateInput {
  disconnect: [StoreWhereUniqueInput!]
  set: [StoreWhereUniqueInput!]
  create: [StoreCreateInput!]
  connect: [StoreWhereUniqueInput!]
}

input CurrencyUpdateArgs {
  where: CurrencyWhereUniqueInput!
  data: CurrencyUpdateInput!
}

input CurrencyCreateInput {
  code: String
  symbol: String
  symbolNative: String
  name: String
  moneyAmounts: MoneyAmountRelateToManyForCreateInput
  orders: OrderRelateToManyForCreateInput
  payments: PaymentRelateToManyForCreateInput
  regions: RegionRelateToManyForCreateInput
  stores: StoreRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input MoneyAmountRelateToManyForCreateInput {
  create: [MoneyAmountCreateInput!]
  connect: [MoneyAmountWhereUniqueInput!]
}

input PaymentRelateToManyForCreateInput {
  create: [PaymentCreateInput!]
  connect: [PaymentWhereUniqueInput!]
}

input RegionRelateToManyForCreateInput {
  create: [RegionCreateInput!]
  connect: [RegionWhereUniqueInput!]
}

input StoreRelateToManyForCreateInput {
  create: [StoreCreateInput!]
  connect: [StoreWhereUniqueInput!]
}

type CustomShippingOption {
  id: ID!
  price: Int
  metadata: JSON
  shippingOption: ShippingOption
  cart: Cart
  createdAt: DateTime
  updatedAt: DateTime
}

input CustomShippingOptionWhereUniqueInput {
  id: ID
}

input CustomShippingOptionWhereInput {
  AND: [CustomShippingOptionWhereInput!]
  OR: [CustomShippingOptionWhereInput!]
  NOT: [CustomShippingOptionWhereInput!]
  id: IDFilter
  price: IntFilter
  shippingOption: ShippingOptionWhereInput
  cart: CartWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CustomShippingOptionOrderByInput {
  id: OrderDirection
  price: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CustomShippingOptionUpdateInput {
  price: Int
  metadata: JSON
  shippingOption: ShippingOptionRelateToOneForUpdateInput
  cart: CartRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRelateToOneForUpdateInput {
  create: ShippingOptionCreateInput
  connect: ShippingOptionWhereUniqueInput
  disconnect: Boolean
}

input CustomShippingOptionUpdateArgs {
  where: CustomShippingOptionWhereUniqueInput!
  data: CustomShippingOptionUpdateInput!
}

input CustomShippingOptionCreateInput {
  price: Int
  metadata: JSON
  shippingOption: ShippingOptionRelateToOneForCreateInput
  cart: CartRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRelateToOneForCreateInput {
  create: ShippingOptionCreateInput
  connect: ShippingOptionWhereUniqueInput
}

type CustomerGroup {
  id: ID!
  name: String
  metadata: JSON
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  usersCount(where: UserWhereInput! = {}): Int
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  priceLists(where: PriceListWhereInput! = {}, orderBy: [PriceListOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceListWhereUniqueInput): [PriceList!]
  priceListsCount(where: PriceListWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input CustomerGroupWhereUniqueInput {
  id: ID
}

input CustomerGroupWhereInput {
  AND: [CustomerGroupWhereInput!]
  OR: [CustomerGroupWhereInput!]
  NOT: [CustomerGroupWhereInput!]
  id: IDFilter
  name: StringFilter
  users: UserManyRelationFilter
  discountConditions: DiscountConditionManyRelationFilter
  priceLists: PriceListManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input UserManyRelationFilter {
  every: UserWhereInput
  some: UserWhereInput
  none: UserWhereInput
}

input DiscountConditionManyRelationFilter {
  every: DiscountConditionWhereInput
  some: DiscountConditionWhereInput
  none: DiscountConditionWhereInput
}

input PriceListManyRelationFilter {
  every: PriceListWhereInput
  some: PriceListWhereInput
  none: PriceListWhereInput
}

input CustomerGroupOrderByInput {
  id: OrderDirection
  name: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input CustomerGroupUpdateInput {
  name: String
  metadata: JSON
  users: UserRelateToManyForUpdateInput
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  priceLists: PriceListRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input UserRelateToManyForUpdateInput {
  disconnect: [UserWhereUniqueInput!]
  set: [UserWhereUniqueInput!]
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input DiscountConditionRelateToManyForUpdateInput {
  disconnect: [DiscountConditionWhereUniqueInput!]
  set: [DiscountConditionWhereUniqueInput!]
  create: [DiscountConditionCreateInput!]
  connect: [DiscountConditionWhereUniqueInput!]
}

input PriceListRelateToManyForUpdateInput {
  disconnect: [PriceListWhereUniqueInput!]
  set: [PriceListWhereUniqueInput!]
  create: [PriceListCreateInput!]
  connect: [PriceListWhereUniqueInput!]
}

input CustomerGroupUpdateArgs {
  where: CustomerGroupWhereUniqueInput!
  data: CustomerGroupUpdateInput!
}

input CustomerGroupCreateInput {
  name: String
  metadata: JSON
  users: UserRelateToManyForCreateInput
  discountConditions: DiscountConditionRelateToManyForCreateInput
  priceLists: PriceListRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input UserRelateToManyForCreateInput {
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input DiscountConditionRelateToManyForCreateInput {
  create: [DiscountConditionCreateInput!]
  connect: [DiscountConditionWhereUniqueInput!]
}

input PriceListRelateToManyForCreateInput {
  create: [PriceListCreateInput!]
  connect: [PriceListWhereUniqueInput!]
}

type Discount {
  id: ID!
  code: String
  isDynamic: Boolean
  isDisabled: Boolean
  stackable: Boolean
  startsAt: DateTime
  endsAt: DateTime
  metadata: JSON
  usageLimit: Int
  usageCount: Int
  validDuration: String
  discountRule: DiscountRule
  carts(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsCount(where: CartWhereInput! = {}): Int
  lineItemAdjustments(where: LineItemAdjustmentWhereInput! = {}, orderBy: [LineItemAdjustmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemAdjustmentWhereUniqueInput): [LineItemAdjustment!]
  lineItemAdjustmentsCount(where: LineItemAdjustmentWhereInput! = {}): Int
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountWhereUniqueInput {
  id: ID
  code: String
}

input DiscountWhereInput {
  AND: [DiscountWhereInput!]
  OR: [DiscountWhereInput!]
  NOT: [DiscountWhereInput!]
  id: IDFilter
  code: StringFilter
  isDynamic: BooleanFilter
  isDisabled: BooleanFilter
  stackable: BooleanFilter
  startsAt: DateTimeFilter
  endsAt: DateTimeNullableFilter
  usageLimit: IntNullableFilter
  usageCount: IntFilter
  validDuration: StringFilter
  discountRule: DiscountRuleWhereInput
  carts: CartManyRelationFilter
  lineItemAdjustments: LineItemAdjustmentManyRelationFilter
  regions: RegionManyRelationFilter
  orders: OrderManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input LineItemAdjustmentManyRelationFilter {
  every: LineItemAdjustmentWhereInput
  some: LineItemAdjustmentWhereInput
  none: LineItemAdjustmentWhereInput
}

input DiscountOrderByInput {
  id: OrderDirection
  code: OrderDirection
  isDynamic: OrderDirection
  isDisabled: OrderDirection
  stackable: OrderDirection
  startsAt: OrderDirection
  endsAt: OrderDirection
  usageLimit: OrderDirection
  usageCount: OrderDirection
  validDuration: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input DiscountUpdateInput {
  code: String
  isDynamic: Boolean
  isDisabled: Boolean
  stackable: Boolean
  startsAt: DateTime
  endsAt: DateTime
  metadata: JSON
  usageLimit: Int
  usageCount: Int
  validDuration: String
  discountRule: DiscountRuleRelateToOneForUpdateInput
  carts: CartRelateToManyForUpdateInput
  lineItemAdjustments: LineItemAdjustmentRelateToManyForUpdateInput
  regions: RegionRelateToManyForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountRuleRelateToOneForUpdateInput {
  create: DiscountRuleCreateInput
  connect: DiscountRuleWhereUniqueInput
  disconnect: Boolean
}

input LineItemAdjustmentRelateToManyForUpdateInput {
  disconnect: [LineItemAdjustmentWhereUniqueInput!]
  set: [LineItemAdjustmentWhereUniqueInput!]
  create: [LineItemAdjustmentCreateInput!]
  connect: [LineItemAdjustmentWhereUniqueInput!]
}

input DiscountUpdateArgs {
  where: DiscountWhereUniqueInput!
  data: DiscountUpdateInput!
}

input DiscountCreateInput {
  code: String
  isDynamic: Boolean
  isDisabled: Boolean
  stackable: Boolean
  startsAt: DateTime
  endsAt: DateTime
  metadata: JSON
  usageLimit: Int
  usageCount: Int
  validDuration: String
  discountRule: DiscountRuleRelateToOneForCreateInput
  carts: CartRelateToManyForCreateInput
  lineItemAdjustments: LineItemAdjustmentRelateToManyForCreateInput
  regions: RegionRelateToManyForCreateInput
  orders: OrderRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountRuleRelateToOneForCreateInput {
  create: DiscountRuleCreateInput
  connect: DiscountRuleWhereUniqueInput
}

input LineItemAdjustmentRelateToManyForCreateInput {
  create: [LineItemAdjustmentCreateInput!]
  connect: [LineItemAdjustmentWhereUniqueInput!]
}

type DiscountCondition {
  id: ID!
  type: DiscountConditionTypeType
  operator: DiscountConditionOperatorType
  metadata: JSON
  discountRule: DiscountRule
  customerGroups(where: CustomerGroupWhereInput! = {}, orderBy: [CustomerGroupOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomerGroupWhereUniqueInput): [CustomerGroup!]
  customerGroupsCount(where: CustomerGroupWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  productCollections(where: ProductCollectionWhereInput! = {}, orderBy: [ProductCollectionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCollectionWhereUniqueInput): [ProductCollection!]
  productCollectionsCount(where: ProductCollectionWhereInput! = {}): Int
  productCategories(where: ProductCategoryWhereInput! = {}, orderBy: [ProductCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCategoryWhereUniqueInput): [ProductCategory!]
  productCategoriesCount(where: ProductCategoryWhereInput! = {}): Int
  productTags(where: ProductTagWhereInput! = {}, orderBy: [ProductTagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTagWhereUniqueInput): [ProductTag!]
  productTagsCount(where: ProductTagWhereInput! = {}): Int
  productTypes(where: ProductTypeWhereInput! = {}, orderBy: [ProductTypeOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTypeWhereUniqueInput): [ProductType!]
  productTypesCount(where: ProductTypeWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum DiscountConditionTypeType {
  products
  product_types
  product_collections
  product_tags
  customer_groups
}

enum DiscountConditionOperatorType {
  in
  not_in
}

input DiscountConditionWhereUniqueInput {
  id: ID
}

input DiscountConditionWhereInput {
  AND: [DiscountConditionWhereInput!]
  OR: [DiscountConditionWhereInput!]
  NOT: [DiscountConditionWhereInput!]
  id: IDFilter
  type: DiscountConditionTypeTypeNullableFilter
  operator: DiscountConditionOperatorTypeNullableFilter
  discountRule: DiscountRuleWhereInput
  customerGroups: CustomerGroupManyRelationFilter
  products: ProductManyRelationFilter
  productCollections: ProductCollectionManyRelationFilter
  productCategories: ProductCategoryManyRelationFilter
  productTags: ProductTagManyRelationFilter
  productTypes: ProductTypeManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input DiscountConditionTypeTypeNullableFilter {
  equals: DiscountConditionTypeType
  in: [DiscountConditionTypeType!]
  notIn: [DiscountConditionTypeType!]
  not: DiscountConditionTypeTypeNullableFilter
}

input DiscountConditionOperatorTypeNullableFilter {
  equals: DiscountConditionOperatorType
  in: [DiscountConditionOperatorType!]
  notIn: [DiscountConditionOperatorType!]
  not: DiscountConditionOperatorTypeNullableFilter
}

input CustomerGroupManyRelationFilter {
  every: CustomerGroupWhereInput
  some: CustomerGroupWhereInput
  none: CustomerGroupWhereInput
}

input ProductManyRelationFilter {
  every: ProductWhereInput
  some: ProductWhereInput
  none: ProductWhereInput
}

input ProductCollectionManyRelationFilter {
  every: ProductCollectionWhereInput
  some: ProductCollectionWhereInput
  none: ProductCollectionWhereInput
}

input ProductCategoryManyRelationFilter {
  every: ProductCategoryWhereInput
  some: ProductCategoryWhereInput
  none: ProductCategoryWhereInput
}

input ProductTagManyRelationFilter {
  every: ProductTagWhereInput
  some: ProductTagWhereInput
  none: ProductTagWhereInput
}

input ProductTypeManyRelationFilter {
  every: ProductTypeWhereInput
  some: ProductTypeWhereInput
  none: ProductTypeWhereInput
}

input DiscountConditionOrderByInput {
  id: OrderDirection
  type: OrderDirection
  operator: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input DiscountConditionUpdateInput {
  type: DiscountConditionTypeType
  operator: DiscountConditionOperatorType
  metadata: JSON
  discountRule: DiscountRuleRelateToOneForUpdateInput
  customerGroups: CustomerGroupRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  productCollections: ProductCollectionRelateToManyForUpdateInput
  productCategories: ProductCategoryRelateToManyForUpdateInput
  productTags: ProductTagRelateToManyForUpdateInput
  productTypes: ProductTypeRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CustomerGroupRelateToManyForUpdateInput {
  disconnect: [CustomerGroupWhereUniqueInput!]
  set: [CustomerGroupWhereUniqueInput!]
  create: [CustomerGroupCreateInput!]
  connect: [CustomerGroupWhereUniqueInput!]
}

input ProductRelateToManyForUpdateInput {
  disconnect: [ProductWhereUniqueInput!]
  set: [ProductWhereUniqueInput!]
  create: [ProductCreateInput!]
  connect: [ProductWhereUniqueInput!]
}

input ProductCollectionRelateToManyForUpdateInput {
  disconnect: [ProductCollectionWhereUniqueInput!]
  set: [ProductCollectionWhereUniqueInput!]
  create: [ProductCollectionCreateInput!]
  connect: [ProductCollectionWhereUniqueInput!]
}

input ProductCategoryRelateToManyForUpdateInput {
  disconnect: [ProductCategoryWhereUniqueInput!]
  set: [ProductCategoryWhereUniqueInput!]
  create: [ProductCategoryCreateInput!]
  connect: [ProductCategoryWhereUniqueInput!]
}

input ProductTagRelateToManyForUpdateInput {
  disconnect: [ProductTagWhereUniqueInput!]
  set: [ProductTagWhereUniqueInput!]
  create: [ProductTagCreateInput!]
  connect: [ProductTagWhereUniqueInput!]
}

input ProductTypeRelateToManyForUpdateInput {
  disconnect: [ProductTypeWhereUniqueInput!]
  set: [ProductTypeWhereUniqueInput!]
  create: [ProductTypeCreateInput!]
  connect: [ProductTypeWhereUniqueInput!]
}

input DiscountConditionUpdateArgs {
  where: DiscountConditionWhereUniqueInput!
  data: DiscountConditionUpdateInput!
}

input DiscountConditionCreateInput {
  type: DiscountConditionTypeType
  operator: DiscountConditionOperatorType
  metadata: JSON
  discountRule: DiscountRuleRelateToOneForCreateInput
  customerGroups: CustomerGroupRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  productCollections: ProductCollectionRelateToManyForCreateInput
  productCategories: ProductCategoryRelateToManyForCreateInput
  productTags: ProductTagRelateToManyForCreateInput
  productTypes: ProductTypeRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CustomerGroupRelateToManyForCreateInput {
  create: [CustomerGroupCreateInput!]
  connect: [CustomerGroupWhereUniqueInput!]
}

input ProductRelateToManyForCreateInput {
  create: [ProductCreateInput!]
  connect: [ProductWhereUniqueInput!]
}

input ProductCollectionRelateToManyForCreateInput {
  create: [ProductCollectionCreateInput!]
  connect: [ProductCollectionWhereUniqueInput!]
}

input ProductCategoryRelateToManyForCreateInput {
  create: [ProductCategoryCreateInput!]
  connect: [ProductCategoryWhereUniqueInput!]
}

input ProductTagRelateToManyForCreateInput {
  create: [ProductTagCreateInput!]
  connect: [ProductTagWhereUniqueInput!]
}

input ProductTypeRelateToManyForCreateInput {
  create: [ProductTypeCreateInput!]
  connect: [ProductTypeWhereUniqueInput!]
}

type DiscountRule {
  id: ID!
  description: String
  type: DiscountRuleTypeType
  value: Int
  allocation: DiscountRuleAllocationType
  metadata: JSON
  discounts(where: DiscountWhereInput! = {}, orderBy: [DiscountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountWhereUniqueInput): [Discount!]
  discountsCount(where: DiscountWhereInput! = {}): Int
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum DiscountRuleTypeType {
  fixed
  percentage
  free_shipping
}

enum DiscountRuleAllocationType {
  total
  item
}

input DiscountRuleWhereUniqueInput {
  id: ID
}

input DiscountRuleWhereInput {
  AND: [DiscountRuleWhereInput!]
  OR: [DiscountRuleWhereInput!]
  NOT: [DiscountRuleWhereInput!]
  id: IDFilter
  description: StringFilter
  type: DiscountRuleTypeTypeNullableFilter
  value: IntFilter
  allocation: DiscountRuleAllocationTypeNullableFilter
  discounts: DiscountManyRelationFilter
  discountConditions: DiscountConditionManyRelationFilter
  products: ProductManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input DiscountRuleTypeTypeNullableFilter {
  equals: DiscountRuleTypeType
  in: [DiscountRuleTypeType!]
  notIn: [DiscountRuleTypeType!]
  not: DiscountRuleTypeTypeNullableFilter
}

input DiscountRuleAllocationTypeNullableFilter {
  equals: DiscountRuleAllocationType
  in: [DiscountRuleAllocationType!]
  notIn: [DiscountRuleAllocationType!]
  not: DiscountRuleAllocationTypeNullableFilter
}

input DiscountRuleOrderByInput {
  id: OrderDirection
  description: OrderDirection
  type: OrderDirection
  value: OrderDirection
  allocation: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input DiscountRuleUpdateInput {
  description: String
  type: DiscountRuleTypeType
  value: Int
  allocation: DiscountRuleAllocationType
  metadata: JSON
  discounts: DiscountRelateToManyForUpdateInput
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountRuleUpdateArgs {
  where: DiscountRuleWhereUniqueInput!
  data: DiscountRuleUpdateInput!
}

input DiscountRuleCreateInput {
  description: String
  type: DiscountRuleTypeType
  value: Int
  allocation: DiscountRuleAllocationType
  metadata: JSON
  discounts: DiscountRelateToManyForCreateInput
  discountConditions: DiscountConditionRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type DraftOrder {
  id: ID!
  status: DraftOrderStatusType
  displayId: Int
  canceledAt: DateTime
  completedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotificationOrder: Boolean
  cart: Cart
  order: Order
  createdAt: DateTime
  updatedAt: DateTime
}

enum DraftOrderStatusType {
  open
  completed
}

input DraftOrderWhereUniqueInput {
  id: ID
  cart: CartWhereUniqueInput
  order: OrderWhereUniqueInput
}

input DraftOrderWhereInput {
  AND: [DraftOrderWhereInput!]
  OR: [DraftOrderWhereInput!]
  NOT: [DraftOrderWhereInput!]
  id: IDFilter
  status: DraftOrderStatusTypeNullableFilter
  displayId: IntFilter
  canceledAt: DateTimeNullableFilter
  completedAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotificationOrder: BooleanFilter
  cart: CartWhereInput
  order: OrderWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input DraftOrderStatusTypeNullableFilter {
  equals: DraftOrderStatusType
  in: [DraftOrderStatusType!]
  notIn: [DraftOrderStatusType!]
  not: DraftOrderStatusTypeNullableFilter
}

input DraftOrderOrderByInput {
  id: OrderDirection
  status: OrderDirection
  displayId: OrderDirection
  canceledAt: OrderDirection
  completedAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotificationOrder: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input DraftOrderUpdateInput {
  status: DraftOrderStatusType
  displayId: Int
  canceledAt: DateTime
  completedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotificationOrder: Boolean
  cart: CartRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DraftOrderUpdateArgs {
  where: DraftOrderWhereUniqueInput!
  data: DraftOrderUpdateInput!
}

input DraftOrderCreateInput {
  status: DraftOrderStatusType
  displayId: Int
  canceledAt: DateTime
  completedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotificationOrder: Boolean
  cart: CartRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Fulfillment {
  id: ID!
  shippedAt: DateTime
  canceledAt: DateTime
  data: JSON
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  order: Order
  claimOrder: ClaimOrder
  swap: Swap
  fulfillmentProvider: FulfillmentProvider
  fulfillmentItems(where: FulfillmentItemWhereInput! = {}, orderBy: [FulfillmentItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentItemWhereUniqueInput): [FulfillmentItem!]
  fulfillmentItemsCount(where: FulfillmentItemWhereInput! = {}): Int
  shippingLabels(where: ShippingLabelWhereInput! = {}, orderBy: [ShippingLabelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingLabelWhereUniqueInput): [ShippingLabel!]
  shippingLabelsCount(where: ShippingLabelWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentWhereUniqueInput {
  id: ID
}

input FulfillmentWhereInput {
  AND: [FulfillmentWhereInput!]
  OR: [FulfillmentWhereInput!]
  NOT: [FulfillmentWhereInput!]
  id: IDFilter
  shippedAt: DateTimeNullableFilter
  canceledAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotification: BooleanFilter
  order: OrderWhereInput
  claimOrder: ClaimOrderWhereInput
  swap: SwapWhereInput
  fulfillmentProvider: FulfillmentProviderWhereInput
  fulfillmentItems: FulfillmentItemManyRelationFilter
  shippingLabels: ShippingLabelManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input FulfillmentItemManyRelationFilter {
  every: FulfillmentItemWhereInput
  some: FulfillmentItemWhereInput
  none: FulfillmentItemWhereInput
}

input ShippingLabelManyRelationFilter {
  every: ShippingLabelWhereInput
  some: ShippingLabelWhereInput
  none: ShippingLabelWhereInput
}

input FulfillmentOrderByInput {
  id: OrderDirection
  shippedAt: OrderDirection
  canceledAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotification: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input FulfillmentUpdateInput {
  shippedAt: DateTime
  canceledAt: DateTime
  data: JSON
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  order: OrderRelateToOneForUpdateInput
  claimOrder: ClaimOrderRelateToOneForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForUpdateInput
  fulfillmentItems: FulfillmentItemRelateToManyForUpdateInput
  shippingLabels: ShippingLabelRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentProviderRelateToOneForUpdateInput {
  create: FulfillmentProviderCreateInput
  connect: FulfillmentProviderWhereUniqueInput
  disconnect: Boolean
}

input FulfillmentItemRelateToManyForUpdateInput {
  disconnect: [FulfillmentItemWhereUniqueInput!]
  set: [FulfillmentItemWhereUniqueInput!]
  create: [FulfillmentItemCreateInput!]
  connect: [FulfillmentItemWhereUniqueInput!]
}

input ShippingLabelRelateToManyForUpdateInput {
  disconnect: [ShippingLabelWhereUniqueInput!]
  set: [ShippingLabelWhereUniqueInput!]
  create: [ShippingLabelCreateInput!]
  connect: [ShippingLabelWhereUniqueInput!]
}

input FulfillmentUpdateArgs {
  where: FulfillmentWhereUniqueInput!
  data: FulfillmentUpdateInput!
}

input FulfillmentCreateInput {
  shippedAt: DateTime
  canceledAt: DateTime
  data: JSON
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  order: OrderRelateToOneForCreateInput
  claimOrder: ClaimOrderRelateToOneForCreateInput
  swap: SwapRelateToOneForCreateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForCreateInput
  fulfillmentItems: FulfillmentItemRelateToManyForCreateInput
  shippingLabels: ShippingLabelRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentProviderRelateToOneForCreateInput {
  create: FulfillmentProviderCreateInput
  connect: FulfillmentProviderWhereUniqueInput
}

input FulfillmentItemRelateToManyForCreateInput {
  create: [FulfillmentItemCreateInput!]
  connect: [FulfillmentItemWhereUniqueInput!]
}

input ShippingLabelRelateToManyForCreateInput {
  create: [ShippingLabelCreateInput!]
  connect: [ShippingLabelWhereUniqueInput!]
}

type FulfillmentItem {
  id: ID!
  quantity: Int
  fulfillment: Fulfillment
  lineItem: OrderLineItem
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentItemWhereUniqueInput {
  id: ID
}

input FulfillmentItemWhereInput {
  AND: [FulfillmentItemWhereInput!]
  OR: [FulfillmentItemWhereInput!]
  NOT: [FulfillmentItemWhereInput!]
  id: IDFilter
  quantity: IntFilter
  fulfillment: FulfillmentWhereInput
  lineItem: OrderLineItemWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input FulfillmentItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input FulfillmentItemUpdateInput {
  quantity: Int
  fulfillment: FulfillmentRelateToOneForUpdateInput
  lineItem: OrderLineItemRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentRelateToOneForUpdateInput {
  create: FulfillmentCreateInput
  connect: FulfillmentWhereUniqueInput
  disconnect: Boolean
}

input OrderLineItemRelateToOneForUpdateInput {
  create: OrderLineItemCreateInput
  connect: OrderLineItemWhereUniqueInput
  disconnect: Boolean
}

input FulfillmentItemUpdateArgs {
  where: FulfillmentItemWhereUniqueInput!
  data: FulfillmentItemUpdateInput!
}

input FulfillmentItemCreateInput {
  quantity: Int
  fulfillment: FulfillmentRelateToOneForCreateInput
  lineItem: OrderLineItemRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentRelateToOneForCreateInput {
  create: FulfillmentCreateInput
  connect: FulfillmentWhereUniqueInput
}

input OrderLineItemRelateToOneForCreateInput {
  create: OrderLineItemCreateInput
  connect: OrderLineItemWhereUniqueInput
}

type FulfillmentProvider {
  id: ID!
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  fulfillments(where: FulfillmentWhereInput! = {}, orderBy: [FulfillmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentWhereUniqueInput): [Fulfillment!]
  fulfillmentsCount(where: FulfillmentWhereInput! = {}): Int
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  shippingOptions(where: ShippingOptionWhereInput! = {}, orderBy: [ShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionWhereUniqueInput): [ShippingOption!]
  shippingOptionsCount(where: ShippingOptionWhereInput! = {}): Int
  shippingProviders(where: ShippingProviderWhereInput! = {}, orderBy: [ShippingProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingProviderWhereUniqueInput): [ShippingProvider!]
  shippingProvidersCount(where: ShippingProviderWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input FulfillmentProviderWhereUniqueInput {
  id: ID
  code: String
}

input FulfillmentProviderWhereInput {
  AND: [FulfillmentProviderWhereInput!]
  OR: [FulfillmentProviderWhereInput!]
  NOT: [FulfillmentProviderWhereInput!]
  id: IDFilter
  name: StringFilter
  code: StringFilter
  isInstalled: BooleanFilter
  fulfillments: FulfillmentManyRelationFilter
  regions: RegionManyRelationFilter
  shippingOptions: ShippingOptionManyRelationFilter
  shippingProviders: ShippingProviderManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingOptionManyRelationFilter {
  every: ShippingOptionWhereInput
  some: ShippingOptionWhereInput
  none: ShippingOptionWhereInput
}

input FulfillmentProviderOrderByInput {
  id: OrderDirection
  name: OrderDirection
  code: OrderDirection
  isInstalled: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input FulfillmentProviderUpdateInput {
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  fulfillments: FulfillmentRelateToManyForUpdateInput
  regions: RegionRelateToManyForUpdateInput
  shippingOptions: ShippingOptionRelateToManyForUpdateInput
  shippingProviders: ShippingProviderRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRelateToManyForUpdateInput {
  disconnect: [ShippingOptionWhereUniqueInput!]
  set: [ShippingOptionWhereUniqueInput!]
  create: [ShippingOptionCreateInput!]
  connect: [ShippingOptionWhereUniqueInput!]
}

input FulfillmentProviderUpdateArgs {
  where: FulfillmentProviderWhereUniqueInput!
  data: FulfillmentProviderUpdateInput!
}

input FulfillmentProviderCreateInput {
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  fulfillments: FulfillmentRelateToManyForCreateInput
  regions: RegionRelateToManyForCreateInput
  shippingOptions: ShippingOptionRelateToManyForCreateInput
  shippingProviders: ShippingProviderRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRelateToManyForCreateInput {
  create: [ShippingOptionCreateInput!]
  connect: [ShippingOptionWhereUniqueInput!]
}

type GiftCard {
  id: ID!
  code: String
  value: Int
  balance: Int
  isDisabled: Boolean
  endsAt: DateTime
  metadata: JSON
  order: Order
  carts(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsCount(where: CartWhereInput! = {}): Int
  giftCardTransactions(where: GiftCardTransactionWhereInput! = {}, orderBy: [GiftCardTransactionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardTransactionWhereUniqueInput): [GiftCardTransaction!]
  giftCardTransactionsCount(where: GiftCardTransactionWhereInput! = {}): Int
  region: Region
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardWhereUniqueInput {
  id: ID
  code: String
}

input GiftCardWhereInput {
  AND: [GiftCardWhereInput!]
  OR: [GiftCardWhereInput!]
  NOT: [GiftCardWhereInput!]
  id: IDFilter
  code: StringFilter
  value: IntFilter
  balance: IntFilter
  isDisabled: BooleanFilter
  endsAt: DateTimeNullableFilter
  order: OrderWhereInput
  carts: CartManyRelationFilter
  giftCardTransactions: GiftCardTransactionManyRelationFilter
  region: RegionWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input GiftCardTransactionManyRelationFilter {
  every: GiftCardTransactionWhereInput
  some: GiftCardTransactionWhereInput
  none: GiftCardTransactionWhereInput
}

input GiftCardOrderByInput {
  id: OrderDirection
  code: OrderDirection
  value: OrderDirection
  balance: OrderDirection
  isDisabled: OrderDirection
  endsAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input GiftCardUpdateInput {
  code: String
  value: Int
  balance: Int
  isDisabled: Boolean
  endsAt: DateTime
  metadata: JSON
  order: OrderRelateToOneForUpdateInput
  carts: CartRelateToManyForUpdateInput
  giftCardTransactions: GiftCardTransactionRelateToManyForUpdateInput
  region: RegionRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardTransactionRelateToManyForUpdateInput {
  disconnect: [GiftCardTransactionWhereUniqueInput!]
  set: [GiftCardTransactionWhereUniqueInput!]
  create: [GiftCardTransactionCreateInput!]
  connect: [GiftCardTransactionWhereUniqueInput!]
}

input GiftCardUpdateArgs {
  where: GiftCardWhereUniqueInput!
  data: GiftCardUpdateInput!
}

input GiftCardCreateInput {
  code: String
  value: Int
  balance: Int
  isDisabled: Boolean
  endsAt: DateTime
  metadata: JSON
  order: OrderRelateToOneForCreateInput
  carts: CartRelateToManyForCreateInput
  giftCardTransactions: GiftCardTransactionRelateToManyForCreateInput
  region: RegionRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardTransactionRelateToManyForCreateInput {
  create: [GiftCardTransactionCreateInput!]
  connect: [GiftCardTransactionWhereUniqueInput!]
}

type GiftCardTransaction {
  id: ID!
  amount: Int
  isTaxable: Boolean
  taxRate: Float
  giftCard: GiftCard
  order: Order
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardTransactionWhereUniqueInput {
  id: ID
}

input GiftCardTransactionWhereInput {
  AND: [GiftCardTransactionWhereInput!]
  OR: [GiftCardTransactionWhereInput!]
  NOT: [GiftCardTransactionWhereInput!]
  id: IDFilter
  amount: IntFilter
  isTaxable: BooleanFilter
  taxRate: FloatNullableFilter
  giftCard: GiftCardWhereInput
  order: OrderWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input FloatNullableFilter {
  equals: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
  not: FloatNullableFilter
}

input GiftCardTransactionOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  isTaxable: OrderDirection
  taxRate: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input GiftCardTransactionUpdateInput {
  amount: Int
  isTaxable: Boolean
  taxRate: Float
  giftCard: GiftCardRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardRelateToOneForUpdateInput {
  create: GiftCardCreateInput
  connect: GiftCardWhereUniqueInput
  disconnect: Boolean
}

input GiftCardTransactionUpdateArgs {
  where: GiftCardTransactionWhereUniqueInput!
  data: GiftCardTransactionUpdateInput!
}

input GiftCardTransactionCreateInput {
  amount: Int
  isTaxable: Boolean
  taxRate: Float
  giftCard: GiftCardRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input GiftCardRelateToOneForCreateInput {
  create: GiftCardCreateInput
  connect: GiftCardWhereUniqueInput
}

type IdempotencyKey {
  id: ID!
  idempotencyKey: String
  requestMethod: String
  requestParams: JSON
  requestPath: String
  responseCode: Int
  responseBody: JSON
  recoveryPoint: String
  lockedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input IdempotencyKeyWhereUniqueInput {
  id: ID
  idempotencyKey: String
}

input IdempotencyKeyWhereInput {
  AND: [IdempotencyKeyWhereInput!]
  OR: [IdempotencyKeyWhereInput!]
  NOT: [IdempotencyKeyWhereInput!]
  id: IDFilter
  idempotencyKey: StringFilter
  requestMethod: StringFilter
  requestPath: StringFilter
  responseCode: IntNullableFilter
  recoveryPoint: StringFilter
  lockedAt: DateTimeNullableFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input IdempotencyKeyOrderByInput {
  id: OrderDirection
  idempotencyKey: OrderDirection
  requestMethod: OrderDirection
  requestPath: OrderDirection
  responseCode: OrderDirection
  recoveryPoint: OrderDirection
  lockedAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input IdempotencyKeyUpdateInput {
  idempotencyKey: String
  requestMethod: String
  requestParams: JSON
  requestPath: String
  responseCode: Int
  responseBody: JSON
  recoveryPoint: String
  lockedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input IdempotencyKeyUpdateArgs {
  where: IdempotencyKeyWhereUniqueInput!
  data: IdempotencyKeyUpdateInput!
}

input IdempotencyKeyCreateInput {
  idempotencyKey: String
  requestMethod: String
  requestParams: JSON
  requestPath: String
  responseCode: Int
  responseBody: JSON
  recoveryPoint: String
  lockedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

type Invite {
  id: ID!
  userEmail: String
  role: InviteRoleType
  accepted: Boolean
  metadata: JSON
  token: String
  expiresAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

enum InviteRoleType {
  admin
  member
  developer
}

input InviteWhereUniqueInput {
  id: ID
}

input InviteWhereInput {
  AND: [InviteWhereInput!]
  OR: [InviteWhereInput!]
  NOT: [InviteWhereInput!]
  id: IDFilter
  userEmail: StringFilter
  role: InviteRoleTypeNullableFilter
  accepted: BooleanFilter
  token: StringFilter
  expiresAt: DateTimeFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input InviteRoleTypeNullableFilter {
  equals: InviteRoleType
  in: [InviteRoleType!]
  notIn: [InviteRoleType!]
  not: InviteRoleTypeNullableFilter
}

input InviteOrderByInput {
  id: OrderDirection
  userEmail: OrderDirection
  role: OrderDirection
  accepted: OrderDirection
  token: OrderDirection
  expiresAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input InviteUpdateInput {
  userEmail: String
  role: InviteRoleType
  accepted: Boolean
  metadata: JSON
  token: String
  expiresAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input InviteUpdateArgs {
  where: InviteWhereUniqueInput!
  data: InviteUpdateInput!
}

input InviteCreateInput {
  userEmail: String
  role: InviteRoleType
  accepted: Boolean
  metadata: JSON
  token: String
  expiresAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

type LineItem {
  id: ID!
  quantity: Int
  metadata: JSON
  isReturn: Boolean
  isGiftcard: Boolean
  shouldMerge: Boolean
  allowDiscounts: Boolean
  hasShipping: Boolean
  claimOrder: ClaimOrder
  cart: Cart
  swap: Swap
  productVariant: ProductVariant
  claimItems(where: ClaimItemWhereInput! = {}, orderBy: [ClaimItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimItemWhereUniqueInput): [ClaimItem!]
  claimItemsCount(where: ClaimItemWhereInput! = {}): Int
  lineItemAdjustments(where: LineItemAdjustmentWhereInput! = {}, orderBy: [LineItemAdjustmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemAdjustmentWhereUniqueInput): [LineItemAdjustment!]
  lineItemAdjustmentsCount(where: LineItemAdjustmentWhereInput! = {}): Int
  lineItemTaxLines(where: LineItemTaxLineWhereInput! = {}, orderBy: [LineItemTaxLineOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemTaxLineWhereUniqueInput): [LineItemTaxLine!]
  lineItemTaxLinesCount(where: LineItemTaxLineWhereInput! = {}): Int
  returnItems(where: ReturnItemWhereInput! = {}, orderBy: [ReturnItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnItemWhereUniqueInput): [ReturnItem!]
  returnItemsCount(where: ReturnItemWhereInput! = {}): Int
  title: String
  thumbnail: String
  description: JSON
  originalPrice: String
  unitPrice: String
  total: String
  availableInRegion: String
  percentageOff: Int
  fulfillmentStatus: String
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemWhereUniqueInput {
  id: ID
}

input LineItemWhereInput {
  AND: [LineItemWhereInput!]
  OR: [LineItemWhereInput!]
  NOT: [LineItemWhereInput!]
  id: IDFilter
  quantity: IntFilter
  isReturn: BooleanFilter
  isGiftcard: BooleanFilter
  shouldMerge: BooleanFilter
  allowDiscounts: BooleanFilter
  hasShipping: BooleanFilter
  claimOrder: ClaimOrderWhereInput
  cart: CartWhereInput
  swap: SwapWhereInput
  productVariant: ProductVariantWhereInput
  claimItems: ClaimItemManyRelationFilter
  lineItemAdjustments: LineItemAdjustmentManyRelationFilter
  lineItemTaxLines: LineItemTaxLineManyRelationFilter
  returnItems: ReturnItemManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input LineItemTaxLineManyRelationFilter {
  every: LineItemTaxLineWhereInput
  some: LineItemTaxLineWhereInput
  none: LineItemTaxLineWhereInput
}

input ReturnItemManyRelationFilter {
  every: ReturnItemWhereInput
  some: ReturnItemWhereInput
  none: ReturnItemWhereInput
}

input LineItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  isReturn: OrderDirection
  isGiftcard: OrderDirection
  shouldMerge: OrderDirection
  allowDiscounts: OrderDirection
  hasShipping: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LineItemUpdateInput {
  quantity: Int
  metadata: JSON
  isReturn: Boolean
  isGiftcard: Boolean
  shouldMerge: Boolean
  allowDiscounts: Boolean
  hasShipping: Boolean
  claimOrder: ClaimOrderRelateToOneForUpdateInput
  cart: CartRelateToOneForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  productVariant: ProductVariantRelateToOneForUpdateInput
  claimItems: ClaimItemRelateToManyForUpdateInput
  lineItemAdjustments: LineItemAdjustmentRelateToManyForUpdateInput
  lineItemTaxLines: LineItemTaxLineRelateToManyForUpdateInput
  returnItems: ReturnItemRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemTaxLineRelateToManyForUpdateInput {
  disconnect: [LineItemTaxLineWhereUniqueInput!]
  set: [LineItemTaxLineWhereUniqueInput!]
  create: [LineItemTaxLineCreateInput!]
  connect: [LineItemTaxLineWhereUniqueInput!]
}

input ReturnItemRelateToManyForUpdateInput {
  disconnect: [ReturnItemWhereUniqueInput!]
  set: [ReturnItemWhereUniqueInput!]
  create: [ReturnItemCreateInput!]
  connect: [ReturnItemWhereUniqueInput!]
}

input LineItemUpdateArgs {
  where: LineItemWhereUniqueInput!
  data: LineItemUpdateInput!
}

input LineItemCreateInput {
  quantity: Int
  metadata: JSON
  isReturn: Boolean
  isGiftcard: Boolean
  shouldMerge: Boolean
  allowDiscounts: Boolean
  hasShipping: Boolean
  claimOrder: ClaimOrderRelateToOneForCreateInput
  cart: CartRelateToOneForCreateInput
  swap: SwapRelateToOneForCreateInput
  productVariant: ProductVariantRelateToOneForCreateInput
  claimItems: ClaimItemRelateToManyForCreateInput
  lineItemAdjustments: LineItemAdjustmentRelateToManyForCreateInput
  lineItemTaxLines: LineItemTaxLineRelateToManyForCreateInput
  returnItems: ReturnItemRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemTaxLineRelateToManyForCreateInput {
  create: [LineItemTaxLineCreateInput!]
  connect: [LineItemTaxLineWhereUniqueInput!]
}

input ReturnItemRelateToManyForCreateInput {
  create: [ReturnItemCreateInput!]
  connect: [ReturnItemWhereUniqueInput!]
}

type LineItemAdjustment {
  id: ID!
  description: String
  amount: Int
  metadata: JSON
  discount: Discount
  lineItem: LineItem
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemAdjustmentWhereUniqueInput {
  id: ID
}

input LineItemAdjustmentWhereInput {
  AND: [LineItemAdjustmentWhereInput!]
  OR: [LineItemAdjustmentWhereInput!]
  NOT: [LineItemAdjustmentWhereInput!]
  id: IDFilter
  description: StringFilter
  amount: IntFilter
  discount: DiscountWhereInput
  lineItem: LineItemWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input LineItemAdjustmentOrderByInput {
  id: OrderDirection
  description: OrderDirection
  amount: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LineItemAdjustmentUpdateInput {
  description: String
  amount: Int
  metadata: JSON
  discount: DiscountRelateToOneForUpdateInput
  lineItem: LineItemRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountRelateToOneForUpdateInput {
  create: DiscountCreateInput
  connect: DiscountWhereUniqueInput
  disconnect: Boolean
}

input LineItemAdjustmentUpdateArgs {
  where: LineItemAdjustmentWhereUniqueInput!
  data: LineItemAdjustmentUpdateInput!
}

input LineItemAdjustmentCreateInput {
  description: String
  amount: Int
  metadata: JSON
  discount: DiscountRelateToOneForCreateInput
  lineItem: LineItemRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input DiscountRelateToOneForCreateInput {
  create: DiscountCreateInput
  connect: DiscountWhereUniqueInput
}

type LineItemTaxLine {
  id: ID!
  rate: Float
  name: String
  code: String
  metadata: JSON
  lineItem: LineItem
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemTaxLineWhereUniqueInput {
  id: ID
}

input LineItemTaxLineWhereInput {
  AND: [LineItemTaxLineWhereInput!]
  OR: [LineItemTaxLineWhereInput!]
  NOT: [LineItemTaxLineWhereInput!]
  id: IDFilter
  rate: FloatFilter
  name: StringFilter
  code: StringFilter
  lineItem: LineItemWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input FloatFilter {
  equals: Float
  in: [Float!]
  notIn: [Float!]
  lt: Float
  lte: Float
  gt: Float
  gte: Float
  not: FloatFilter
}

input LineItemTaxLineOrderByInput {
  id: OrderDirection
  rate: OrderDirection
  name: OrderDirection
  code: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LineItemTaxLineUpdateInput {
  rate: Float
  name: String
  code: String
  metadata: JSON
  lineItem: LineItemRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LineItemTaxLineUpdateArgs {
  where: LineItemTaxLineWhereUniqueInput!
  data: LineItemTaxLineUpdateInput!
}

input LineItemTaxLineCreateInput {
  rate: Float
  name: String
  code: String
  metadata: JSON
  lineItem: LineItemRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Location {
  id: ID!
  name: String
  description: String
  address: String
  variants(where: ProductVariantWhereInput! = {}, orderBy: [ProductVariantOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductVariantWhereUniqueInput): [ProductVariant!]
  variantsCount(where: ProductVariantWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input LocationWhereUniqueInput {
  id: ID
}

input LocationWhereInput {
  AND: [LocationWhereInput!]
  OR: [LocationWhereInput!]
  NOT: [LocationWhereInput!]
  id: IDFilter
  name: StringFilter
  description: StringFilter
  address: StringFilter
  variants: ProductVariantManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductVariantManyRelationFilter {
  every: ProductVariantWhereInput
  some: ProductVariantWhereInput
  none: ProductVariantWhereInput
}

input LocationOrderByInput {
  id: OrderDirection
  name: OrderDirection
  description: OrderDirection
  address: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input LocationUpdateInput {
  name: String
  description: String
  address: String
  variants: ProductVariantRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductVariantRelateToManyForUpdateInput {
  disconnect: [ProductVariantWhereUniqueInput!]
  set: [ProductVariantWhereUniqueInput!]
  create: [ProductVariantCreateInput!]
  connect: [ProductVariantWhereUniqueInput!]
}

input LocationUpdateArgs {
  where: LocationWhereUniqueInput!
  data: LocationUpdateInput!
}

input LocationCreateInput {
  name: String
  description: String
  address: String
  variants: ProductVariantRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductVariantRelateToManyForCreateInput {
  create: [ProductVariantCreateInput!]
  connect: [ProductVariantWhereUniqueInput!]
}

type Measurement {
  id: ID!
  value: Float
  unit: String
  type: String
  productVariant: ProductVariant
  createdAt: DateTime
  updatedAt: DateTime
}

input MeasurementWhereUniqueInput {
  id: ID
}

input MeasurementWhereInput {
  AND: [MeasurementWhereInput!]
  OR: [MeasurementWhereInput!]
  NOT: [MeasurementWhereInput!]
  id: IDFilter
  value: FloatFilter
  unit: StringFilter
  type: StringFilter
  productVariant: ProductVariantWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input MeasurementOrderByInput {
  id: OrderDirection
  value: OrderDirection
  unit: OrderDirection
  type: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input MeasurementUpdateInput {
  value: Float
  unit: String
  type: String
  productVariant: ProductVariantRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input MeasurementUpdateArgs {
  where: MeasurementWhereUniqueInput!
  data: MeasurementUpdateInput!
}

input MeasurementCreateInput {
  value: Float
  unit: String
  type: String
  productVariant: ProductVariantRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type MoneyAmount {
  id: ID!
  amount: Int
  compareAmount: Int
  minQuantity: Int
  maxQuantity: Int
  productVariant: ProductVariant
  region: Region
  currency: Currency
  priceList: PriceList
  priceSet: PriceSet
  priceRules(where: PriceRuleWhereInput! = {}, orderBy: [PriceRuleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceRuleWhereUniqueInput): [PriceRule!]
  priceRulesCount(where: PriceRuleWhereInput! = {}): Int
  displayPrice: String
  calculatedPrice: CalculatedPrice
  createdAt: DateTime
  updatedAt: DateTime
}

type CalculatedPrice {
  calculatedAmount: Int
  originalAmount: Int
  currencyCode: String
  moneyAmountId: ID
  variantId: ID
  priceListId: ID
  priceListType: String
}

input MoneyAmountWhereUniqueInput {
  id: ID
}

input MoneyAmountWhereInput {
  AND: [MoneyAmountWhereInput!]
  OR: [MoneyAmountWhereInput!]
  NOT: [MoneyAmountWhereInput!]
  id: IDFilter
  amount: IntFilter
  compareAmount: IntNullableFilter
  minQuantity: IntNullableFilter
  maxQuantity: IntNullableFilter
  productVariant: ProductVariantWhereInput
  region: RegionWhereInput
  currency: CurrencyWhereInput
  priceList: PriceListWhereInput
  priceSet: PriceSetWhereInput
  priceRules: PriceRuleManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PriceRuleManyRelationFilter {
  every: PriceRuleWhereInput
  some: PriceRuleWhereInput
  none: PriceRuleWhereInput
}

input MoneyAmountOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  compareAmount: OrderDirection
  minQuantity: OrderDirection
  maxQuantity: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input MoneyAmountUpdateInput {
  amount: Int
  compareAmount: Int
  minQuantity: Int
  maxQuantity: Int
  productVariant: ProductVariantRelateToOneForUpdateInput
  region: RegionRelateToOneForUpdateInput
  currency: CurrencyRelateToOneForUpdateInput
  priceList: PriceListRelateToOneForUpdateInput
  priceSet: PriceSetRelateToOneForUpdateInput
  priceRules: PriceRuleRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CurrencyRelateToOneForUpdateInput {
  create: CurrencyCreateInput
  connect: CurrencyWhereUniqueInput
  disconnect: Boolean
}

input PriceListRelateToOneForUpdateInput {
  create: PriceListCreateInput
  connect: PriceListWhereUniqueInput
  disconnect: Boolean
}

input PriceSetRelateToOneForUpdateInput {
  create: PriceSetCreateInput
  connect: PriceSetWhereUniqueInput
  disconnect: Boolean
}

input PriceRuleRelateToManyForUpdateInput {
  disconnect: [PriceRuleWhereUniqueInput!]
  set: [PriceRuleWhereUniqueInput!]
  create: [PriceRuleCreateInput!]
  connect: [PriceRuleWhereUniqueInput!]
}

input MoneyAmountUpdateArgs {
  where: MoneyAmountWhereUniqueInput!
  data: MoneyAmountUpdateInput!
}

input MoneyAmountCreateInput {
  amount: Int
  compareAmount: Int
  minQuantity: Int
  maxQuantity: Int
  productVariant: ProductVariantRelateToOneForCreateInput
  region: RegionRelateToOneForCreateInput
  currency: CurrencyRelateToOneForCreateInput
  priceList: PriceListRelateToOneForCreateInput
  priceSet: PriceSetRelateToOneForCreateInput
  priceRules: PriceRuleRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CurrencyRelateToOneForCreateInput {
  create: CurrencyCreateInput
  connect: CurrencyWhereUniqueInput
}

input PriceListRelateToOneForCreateInput {
  create: PriceListCreateInput
  connect: PriceListWhereUniqueInput
}

input PriceSetRelateToOneForCreateInput {
  create: PriceSetCreateInput
  connect: PriceSetWhereUniqueInput
}

input PriceRuleRelateToManyForCreateInput {
  create: [PriceRuleCreateInput!]
  connect: [PriceRuleWhereUniqueInput!]
}

type Note {
  id: ID!
  value: String
  resourceType: String
  resourceId: String
  authorId: String
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input NoteWhereUniqueInput {
  id: ID
}

input NoteWhereInput {
  AND: [NoteWhereInput!]
  OR: [NoteWhereInput!]
  NOT: [NoteWhereInput!]
  id: IDFilter
  value: StringFilter
  resourceType: StringFilter
  resourceId: StringFilter
  authorId: StringFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input NoteOrderByInput {
  id: OrderDirection
  value: OrderDirection
  resourceType: OrderDirection
  resourceId: OrderDirection
  authorId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input NoteUpdateInput {
  value: String
  resourceType: String
  resourceId: String
  authorId: String
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input NoteUpdateArgs {
  where: NoteWhereUniqueInput!
  data: NoteUpdateInput!
}

input NoteCreateInput {
  value: String
  resourceType: String
  resourceId: String
  authorId: String
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

type Notification {
  id: ID!
  eventName: String
  resourceType: String
  resourceId: String
  to: String
  data: JSON
  parentId: String
  notificationProvider: NotificationProvider
  user: User
  otherNotifications(where: NotificationWhereInput! = {}, orderBy: [NotificationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NotificationWhereUniqueInput): [Notification!]
  otherNotificationsCount(where: NotificationWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input NotificationWhereUniqueInput {
  id: ID
}

input NotificationWhereInput {
  AND: [NotificationWhereInput!]
  OR: [NotificationWhereInput!]
  NOT: [NotificationWhereInput!]
  id: IDFilter
  eventName: StringFilter
  resourceType: StringFilter
  resourceId: StringFilter
  to: StringFilter
  parentId: StringFilter
  notificationProvider: NotificationProviderWhereInput
  user: UserWhereInput
  otherNotifications: NotificationManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input NotificationManyRelationFilter {
  every: NotificationWhereInput
  some: NotificationWhereInput
  none: NotificationWhereInput
}

input NotificationOrderByInput {
  id: OrderDirection
  eventName: OrderDirection
  resourceType: OrderDirection
  resourceId: OrderDirection
  to: OrderDirection
  parentId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input NotificationUpdateInput {
  eventName: String
  resourceType: String
  resourceId: String
  to: String
  data: JSON
  parentId: String
  notificationProvider: NotificationProviderRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  otherNotifications: NotificationRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input NotificationProviderRelateToOneForUpdateInput {
  create: NotificationProviderCreateInput
  connect: NotificationProviderWhereUniqueInput
  disconnect: Boolean
}

input NotificationRelateToManyForUpdateInput {
  disconnect: [NotificationWhereUniqueInput!]
  set: [NotificationWhereUniqueInput!]
  create: [NotificationCreateInput!]
  connect: [NotificationWhereUniqueInput!]
}

input NotificationUpdateArgs {
  where: NotificationWhereUniqueInput!
  data: NotificationUpdateInput!
}

input NotificationCreateInput {
  eventName: String
  resourceType: String
  resourceId: String
  to: String
  data: JSON
  parentId: String
  notificationProvider: NotificationProviderRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  otherNotifications: NotificationRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input NotificationProviderRelateToOneForCreateInput {
  create: NotificationProviderCreateInput
  connect: NotificationProviderWhereUniqueInput
}

input NotificationRelateToManyForCreateInput {
  create: [NotificationCreateInput!]
  connect: [NotificationWhereUniqueInput!]
}

type NotificationProvider {
  id: ID!
  isInstalled: Boolean
  notifications(where: NotificationWhereInput! = {}, orderBy: [NotificationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NotificationWhereUniqueInput): [Notification!]
  notificationsCount(where: NotificationWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input NotificationProviderWhereUniqueInput {
  id: ID
}

input NotificationProviderWhereInput {
  AND: [NotificationProviderWhereInput!]
  OR: [NotificationProviderWhereInput!]
  NOT: [NotificationProviderWhereInput!]
  id: IDFilter
  isInstalled: BooleanFilter
  notifications: NotificationManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input NotificationProviderOrderByInput {
  id: OrderDirection
  isInstalled: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input NotificationProviderUpdateInput {
  isInstalled: Boolean
  notifications: NotificationRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input NotificationProviderUpdateArgs {
  where: NotificationProviderWhereUniqueInput!
  data: NotificationProviderUpdateInput!
}

input NotificationProviderCreateInput {
  isInstalled: Boolean
  notifications: NotificationRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type OAuth {
  id: ID!
  displayName: String
  applicationName: String
  installUrl: String
  uninstallUrl: String
  data: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input OAuthWhereUniqueInput {
  id: ID
  applicationName: String
}

input OAuthWhereInput {
  AND: [OAuthWhereInput!]
  OR: [OAuthWhereInput!]
  NOT: [OAuthWhereInput!]
  id: IDFilter
  displayName: StringFilter
  applicationName: StringFilter
  installUrl: StringFilter
  uninstallUrl: StringFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input OAuthOrderByInput {
  id: OrderDirection
  displayName: OrderDirection
  applicationName: OrderDirection
  installUrl: OrderDirection
  uninstallUrl: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OAuthUpdateInput {
  displayName: String
  applicationName: String
  installUrl: String
  uninstallUrl: String
  data: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input OAuthUpdateArgs {
  where: OAuthWhereUniqueInput!
  data: OAuthUpdateInput!
}

input OAuthCreateInput {
  displayName: String
  applicationName: String
  installUrl: String
  uninstallUrl: String
  data: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

type Order {
  id: ID!
  status: OrderStatusType
  displayId: Int
  email: String
  taxRate: Float
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  externalId: String
  shippingAddress: Address
  billingAddress: Address
  currency: Currency
  draftOrder: DraftOrder
  cart: Cart
  user: User
  region: Region
  claimOrders(where: ClaimOrderWhereInput! = {}, orderBy: [ClaimOrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimOrderWhereUniqueInput): [ClaimOrder!]
  claimOrdersCount(where: ClaimOrderWhereInput! = {}): Int
  fulfillments(where: FulfillmentWhereInput! = {}, orderBy: [FulfillmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentWhereUniqueInput): [Fulfillment!]
  fulfillmentsCount(where: FulfillmentWhereInput! = {}): Int
  giftCards(where: GiftCardWhereInput! = {}, orderBy: [GiftCardOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardWhereUniqueInput): [GiftCard!]
  giftCardsCount(where: GiftCardWhereInput! = {}): Int
  giftCardTransactions(where: GiftCardTransactionWhereInput! = {}, orderBy: [GiftCardTransactionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardTransactionWhereUniqueInput): [GiftCardTransaction!]
  giftCardTransactionsCount(where: GiftCardTransactionWhereInput! = {}): Int
  lineItems(where: OrderLineItemWhereInput! = {}, orderBy: [OrderLineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderLineItemWhereUniqueInput): [OrderLineItem!]
  lineItemsCount(where: OrderLineItemWhereInput! = {}): Int
  discounts(where: DiscountWhereInput! = {}, orderBy: [DiscountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountWhereUniqueInput): [Discount!]
  discountsCount(where: DiscountWhereInput! = {}): Int
  payments(where: PaymentWhereInput! = {}, orderBy: [PaymentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentWhereUniqueInput): [Payment!]
  paymentsCount(where: PaymentWhereInput! = {}): Int
  returns(where: ReturnWhereInput! = {}, orderBy: [ReturnOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnWhereUniqueInput): [Return!]
  returnsCount(where: ReturnWhereInput! = {}): Int
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  swaps(where: SwapWhereInput! = {}, orderBy: [SwapOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SwapWhereUniqueInput): [Swap!]
  swapsCount(where: SwapWhereInput! = {}): Int
  secretKey: String
  subtotal: String
  shipping: String
  discount: String
  tax: String
  total: String
  rawTotal: Int
  fulfillmentDetails: JSON
  unfulfilled: JSON
  fulfillmentStatus: JSON
  paymentDetails: JSON
  totalPaid: Int
  formattedTotalPaid: String
  events(where: OrderEventWhereInput! = {}, orderBy: [OrderEventOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderEventWhereUniqueInput): [OrderEvent!]
  eventsCount(where: OrderEventWhereInput! = {}): Int
  note: String
  shippingLabels(where: ShippingLabelWhereInput! = {}, orderBy: [ShippingLabelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingLabelWhereUniqueInput): [ShippingLabel!]
  shippingLabelsCount(where: ShippingLabelWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum OrderStatusType {
  pending
  completed
  archived
  canceled
  requires_action
}

input OrderWhereUniqueInput {
  id: ID
  draftOrder: DraftOrderWhereUniqueInput
  cart: CartWhereUniqueInput
}

input OrderWhereInput {
  AND: [OrderWhereInput!]
  OR: [OrderWhereInput!]
  NOT: [OrderWhereInput!]
  id: IDFilter
  status: OrderStatusTypeNullableFilter
  displayId: IntFilter
  email: StringFilter
  taxRate: FloatNullableFilter
  canceledAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotification: BooleanFilter
  externalId: StringFilter
  shippingAddress: AddressWhereInput
  billingAddress: AddressWhereInput
  currency: CurrencyWhereInput
  draftOrder: DraftOrderWhereInput
  cart: CartWhereInput
  user: UserWhereInput
  region: RegionWhereInput
  claimOrders: ClaimOrderManyRelationFilter
  fulfillments: FulfillmentManyRelationFilter
  giftCards: GiftCardManyRelationFilter
  giftCardTransactions: GiftCardTransactionManyRelationFilter
  lineItems: OrderLineItemManyRelationFilter
  discounts: DiscountManyRelationFilter
  payments: PaymentManyRelationFilter
  returns: ReturnManyRelationFilter
  shippingMethods: ShippingMethodManyRelationFilter
  swaps: SwapManyRelationFilter
  secretKey: StringFilter
  events: OrderEventManyRelationFilter
  note: StringFilter
  shippingLabels: ShippingLabelManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input OrderStatusTypeNullableFilter {
  equals: OrderStatusType
  in: [OrderStatusType!]
  notIn: [OrderStatusType!]
  not: OrderStatusTypeNullableFilter
}

input OrderLineItemManyRelationFilter {
  every: OrderLineItemWhereInput
  some: OrderLineItemWhereInput
  none: OrderLineItemWhereInput
}

input ReturnManyRelationFilter {
  every: ReturnWhereInput
  some: ReturnWhereInput
  none: ReturnWhereInput
}

input OrderEventManyRelationFilter {
  every: OrderEventWhereInput
  some: OrderEventWhereInput
  none: OrderEventWhereInput
}

input OrderOrderByInput {
  id: OrderDirection
  status: OrderDirection
  displayId: OrderDirection
  email: OrderDirection
  taxRate: OrderDirection
  canceledAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotification: OrderDirection
  externalId: OrderDirection
  secretKey: OrderDirection
  note: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OrderUpdateInput {
  status: OrderStatusType
  displayId: Int
  email: String
  taxRate: Float
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  externalId: String
  shippingAddress: AddressRelateToOneForUpdateInput
  billingAddress: AddressRelateToOneForUpdateInput
  currency: CurrencyRelateToOneForUpdateInput
  draftOrder: DraftOrderRelateToOneForUpdateInput
  cart: CartRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  region: RegionRelateToOneForUpdateInput
  claimOrders: ClaimOrderRelateToManyForUpdateInput
  fulfillments: FulfillmentRelateToManyForUpdateInput
  giftCards: GiftCardRelateToManyForUpdateInput
  giftCardTransactions: GiftCardTransactionRelateToManyForUpdateInput
  lineItems: OrderLineItemRelateToManyForUpdateInput
  discounts: DiscountRelateToManyForUpdateInput
  payments: PaymentRelateToManyForUpdateInput
  returns: ReturnRelateToManyForUpdateInput
  shippingMethods: ShippingMethodRelateToManyForUpdateInput
  swaps: SwapRelateToManyForUpdateInput
  secretKey: String
  events: OrderEventRelateToManyForUpdateInput
  note: String
  shippingLabels: ShippingLabelRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderLineItemRelateToManyForUpdateInput {
  disconnect: [OrderLineItemWhereUniqueInput!]
  set: [OrderLineItemWhereUniqueInput!]
  create: [OrderLineItemCreateInput!]
  connect: [OrderLineItemWhereUniqueInput!]
}

input ReturnRelateToManyForUpdateInput {
  disconnect: [ReturnWhereUniqueInput!]
  set: [ReturnWhereUniqueInput!]
  create: [ReturnCreateInput!]
  connect: [ReturnWhereUniqueInput!]
}

input OrderEventRelateToManyForUpdateInput {
  disconnect: [OrderEventWhereUniqueInput!]
  set: [OrderEventWhereUniqueInput!]
  create: [OrderEventCreateInput!]
  connect: [OrderEventWhereUniqueInput!]
}

input OrderUpdateArgs {
  where: OrderWhereUniqueInput!
  data: OrderUpdateInput!
}

input OrderCreateInput {
  status: OrderStatusType
  displayId: Int
  email: String
  taxRate: Float
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  externalId: String
  shippingAddress: AddressRelateToOneForCreateInput
  billingAddress: AddressRelateToOneForCreateInput
  currency: CurrencyRelateToOneForCreateInput
  draftOrder: DraftOrderRelateToOneForCreateInput
  cart: CartRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  region: RegionRelateToOneForCreateInput
  claimOrders: ClaimOrderRelateToManyForCreateInput
  fulfillments: FulfillmentRelateToManyForCreateInput
  giftCards: GiftCardRelateToManyForCreateInput
  giftCardTransactions: GiftCardTransactionRelateToManyForCreateInput
  lineItems: OrderLineItemRelateToManyForCreateInput
  discounts: DiscountRelateToManyForCreateInput
  payments: PaymentRelateToManyForCreateInput
  returns: ReturnRelateToManyForCreateInput
  shippingMethods: ShippingMethodRelateToManyForCreateInput
  swaps: SwapRelateToManyForCreateInput
  secretKey: String
  events: OrderEventRelateToManyForCreateInput
  note: String
  shippingLabels: ShippingLabelRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderLineItemRelateToManyForCreateInput {
  create: [OrderLineItemCreateInput!]
  connect: [OrderLineItemWhereUniqueInput!]
}

input ReturnRelateToManyForCreateInput {
  create: [ReturnCreateInput!]
  connect: [ReturnWhereUniqueInput!]
}

input OrderEventRelateToManyForCreateInput {
  create: [OrderEventCreateInput!]
  connect: [OrderEventWhereUniqueInput!]
}

type OrderEvent {
  id: ID!
  order: Order
  user: User
  type: OrderEventTypeType
  data: JSON
  time: DateTime
  createdBy: User
  createdAt: DateTime
  updatedAt: DateTime
}

enum OrderEventTypeType {
  ORDER_PLACED
  STATUS_CHANGE
  PAYMENT_STATUS_CHANGE
  PAYMENT_CAPTURED
  FULFILLMENT_STATUS_CHANGE
  NOTE_ADDED
  EMAIL_SENT
  TRACKING_NUMBER_ADDED
  RETURN_REQUESTED
  REFUND_PROCESSED
}

input OrderEventWhereUniqueInput {
  id: ID
}

input OrderEventWhereInput {
  AND: [OrderEventWhereInput!]
  OR: [OrderEventWhereInput!]
  NOT: [OrderEventWhereInput!]
  id: IDFilter
  order: OrderWhereInput
  user: UserWhereInput
  type: OrderEventTypeTypeNullableFilter
  time: DateTimeNullableFilter
  createdBy: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input OrderEventTypeTypeNullableFilter {
  equals: OrderEventTypeType
  in: [OrderEventTypeType!]
  notIn: [OrderEventTypeType!]
  not: OrderEventTypeTypeNullableFilter
}

input OrderEventOrderByInput {
  id: OrderDirection
  type: OrderDirection
  time: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OrderEventUpdateInput {
  order: OrderRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  type: OrderEventTypeType
  data: JSON
  time: DateTime
  createdBy: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderEventUpdateArgs {
  where: OrderEventWhereUniqueInput!
  data: OrderEventUpdateInput!
}

input OrderEventCreateInput {
  order: OrderRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  type: OrderEventTypeType
  data: JSON
  time: DateTime
  createdBy: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type OrderLineItem {
  id: ID!
  quantity: Int
  title: String
  sku: String
  thumbnail: String
  metadata: JSON
  productData: JSON
  variantData: JSON
  variantTitle: String
  formattedUnitPrice: String
  formattedTotal: String
  order: Order
  productVariant: ProductVariant
  moneyAmount: OrderMoneyAmount
  originalLineItem: LineItem
  fulfillmentItems(where: FulfillmentItemWhereInput! = {}, orderBy: [FulfillmentItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentItemWhereUniqueInput): [FulfillmentItem!]
  fulfillmentItemsCount(where: FulfillmentItemWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderLineItemWhereUniqueInput {
  id: ID
  moneyAmount: OrderMoneyAmountWhereUniqueInput
}

input OrderLineItemWhereInput {
  AND: [OrderLineItemWhereInput!]
  OR: [OrderLineItemWhereInput!]
  NOT: [OrderLineItemWhereInput!]
  id: IDFilter
  quantity: IntFilter
  title: StringFilter
  sku: StringFilter
  variantTitle: StringFilter
  formattedUnitPrice: StringFilter
  formattedTotal: StringFilter
  order: OrderWhereInput
  productVariant: ProductVariantWhereInput
  moneyAmount: OrderMoneyAmountWhereInput
  originalLineItem: LineItemWhereInput
  fulfillmentItems: FulfillmentItemManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input OrderLineItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  title: OrderDirection
  sku: OrderDirection
  variantTitle: OrderDirection
  formattedUnitPrice: OrderDirection
  formattedTotal: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OrderLineItemUpdateInput {
  quantity: Int
  title: String
  sku: String
  metadata: JSON
  productData: JSON
  variantData: JSON
  variantTitle: String
  formattedUnitPrice: String
  formattedTotal: String
  order: OrderRelateToOneForUpdateInput
  productVariant: ProductVariantRelateToOneForUpdateInput
  moneyAmount: OrderMoneyAmountRelateToOneForUpdateInput
  originalLineItem: LineItemRelateToOneForUpdateInput
  fulfillmentItems: FulfillmentItemRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderMoneyAmountRelateToOneForUpdateInput {
  create: OrderMoneyAmountCreateInput
  connect: OrderMoneyAmountWhereUniqueInput
  disconnect: Boolean
}

input OrderLineItemUpdateArgs {
  where: OrderLineItemWhereUniqueInput!
  data: OrderLineItemUpdateInput!
}

input OrderLineItemCreateInput {
  quantity: Int
  title: String
  sku: String
  metadata: JSON
  productData: JSON
  variantData: JSON
  variantTitle: String
  formattedUnitPrice: String
  formattedTotal: String
  order: OrderRelateToOneForCreateInput
  productVariant: ProductVariantRelateToOneForCreateInput
  moneyAmount: OrderMoneyAmountRelateToOneForCreateInput
  originalLineItem: LineItemRelateToOneForCreateInput
  fulfillmentItems: FulfillmentItemRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderMoneyAmountRelateToOneForCreateInput {
  create: OrderMoneyAmountCreateInput
  connect: OrderMoneyAmountWhereUniqueInput
}

type OrderMoneyAmount {
  id: ID!
  amount: Int
  originalAmount: Int
  priceData: JSON
  metadata: JSON
  orderLineItem: OrderLineItem
  currency: Currency
  region: Region
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderMoneyAmountWhereUniqueInput {
  id: ID
  orderLineItem: OrderLineItemWhereUniqueInput
}

input OrderMoneyAmountWhereInput {
  AND: [OrderMoneyAmountWhereInput!]
  OR: [OrderMoneyAmountWhereInput!]
  NOT: [OrderMoneyAmountWhereInput!]
  id: IDFilter
  amount: IntFilter
  originalAmount: IntFilter
  orderLineItem: OrderLineItemWhereInput
  currency: CurrencyWhereInput
  region: RegionWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input OrderMoneyAmountOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  originalAmount: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input OrderMoneyAmountUpdateInput {
  amount: Int
  originalAmount: Int
  priceData: JSON
  metadata: JSON
  orderLineItem: OrderLineItemRelateToOneForUpdateInput
  currency: CurrencyRelateToOneForUpdateInput
  region: RegionRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input OrderMoneyAmountUpdateArgs {
  where: OrderMoneyAmountWhereUniqueInput!
  data: OrderMoneyAmountUpdateInput!
}

input OrderMoneyAmountCreateInput {
  amount: Int
  originalAmount: Int
  priceData: JSON
  metadata: JSON
  orderLineItem: OrderLineItemRelateToOneForCreateInput
  currency: CurrencyRelateToOneForCreateInput
  region: RegionRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Payment {
  id: ID!
  status: PaymentStatusType
  amount: Int
  currencyCode: String
  amountRefunded: Int
  data: JSON
  capturedAt: DateTime
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  cart: Cart
  paymentCollection: PaymentCollection
  swap: Swap
  currency: Currency
  order: Order
  captures(where: CaptureWhereInput! = {}, orderBy: [CaptureOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CaptureWhereUniqueInput): [Capture!]
  capturesCount(where: CaptureWhereInput! = {}): Int
  refunds(where: RefundWhereInput! = {}, orderBy: [RefundOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RefundWhereUniqueInput): [Refund!]
  refundsCount(where: RefundWhereInput! = {}): Int
  user: User
  paymentLink: String
  createdAt: DateTime
  updatedAt: DateTime
}

enum PaymentStatusType {
  pending
  authorized
  captured
  failed
  canceled
}

input PaymentWhereUniqueInput {
  id: ID
  cart: CartWhereUniqueInput
  swap: SwapWhereUniqueInput
}

input PaymentWhereInput {
  AND: [PaymentWhereInput!]
  OR: [PaymentWhereInput!]
  NOT: [PaymentWhereInput!]
  id: IDFilter
  status: PaymentStatusTypeNullableFilter
  amount: IntFilter
  currencyCode: StringFilter
  amountRefunded: IntFilter
  capturedAt: DateTimeNullableFilter
  canceledAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  cart: CartWhereInput
  paymentCollection: PaymentCollectionWhereInput
  swap: SwapWhereInput
  currency: CurrencyWhereInput
  order: OrderWhereInput
  captures: CaptureManyRelationFilter
  refunds: RefundManyRelationFilter
  user: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PaymentStatusTypeNullableFilter {
  equals: PaymentStatusType
  in: [PaymentStatusType!]
  notIn: [PaymentStatusType!]
  not: PaymentStatusTypeNullableFilter
}

input CaptureManyRelationFilter {
  every: CaptureWhereInput
  some: CaptureWhereInput
  none: CaptureWhereInput
}

input RefundManyRelationFilter {
  every: RefundWhereInput
  some: RefundWhereInput
  none: RefundWhereInput
}

input PaymentOrderByInput {
  id: OrderDirection
  status: OrderDirection
  amount: OrderDirection
  currencyCode: OrderDirection
  amountRefunded: OrderDirection
  capturedAt: OrderDirection
  canceledAt: OrderDirection
  idempotencyKey: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PaymentUpdateInput {
  status: PaymentStatusType
  amount: Int
  currencyCode: String
  amountRefunded: Int
  data: JSON
  capturedAt: DateTime
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  cart: CartRelateToOneForUpdateInput
  paymentCollection: PaymentCollectionRelateToOneForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  currency: CurrencyRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  captures: CaptureRelateToManyForUpdateInput
  refunds: RefundRelateToManyForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CaptureRelateToManyForUpdateInput {
  disconnect: [CaptureWhereUniqueInput!]
  set: [CaptureWhereUniqueInput!]
  create: [CaptureCreateInput!]
  connect: [CaptureWhereUniqueInput!]
}

input RefundRelateToManyForUpdateInput {
  disconnect: [RefundWhereUniqueInput!]
  set: [RefundWhereUniqueInput!]
  create: [RefundCreateInput!]
  connect: [RefundWhereUniqueInput!]
}

input PaymentUpdateArgs {
  where: PaymentWhereUniqueInput!
  data: PaymentUpdateInput!
}

input PaymentCreateInput {
  status: PaymentStatusType
  amount: Int
  currencyCode: String
  amountRefunded: Int
  data: JSON
  capturedAt: DateTime
  canceledAt: DateTime
  metadata: JSON
  idempotencyKey: String
  cart: CartRelateToOneForCreateInput
  paymentCollection: PaymentCollectionRelateToOneForCreateInput
  swap: SwapRelateToOneForCreateInput
  currency: CurrencyRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  captures: CaptureRelateToManyForCreateInput
  refunds: RefundRelateToManyForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CaptureRelateToManyForCreateInput {
  create: [CaptureCreateInput!]
  connect: [CaptureWhereUniqueInput!]
}

input RefundRelateToManyForCreateInput {
  create: [RefundCreateInput!]
  connect: [RefundWhereUniqueInput!]
}

type PaymentCollection {
  id: ID!
  description: PaymentCollectionDescriptionType
  amount: Int
  authorizedAmount: Int
  refundedAmount: Int
  metadata: JSON
  paymentSessions(where: PaymentSessionWhereInput! = {}, orderBy: [PaymentSessionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentSessionWhereUniqueInput): [PaymentSession!]
  paymentSessionsCount(where: PaymentSessionWhereInput! = {}): Int
  payments(where: PaymentWhereInput! = {}, orderBy: [PaymentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentWhereUniqueInput): [Payment!]
  paymentsCount(where: PaymentWhereInput! = {}): Int
  cart: Cart
  createdAt: DateTime
  updatedAt: DateTime
}

enum PaymentCollectionDescriptionType {
  default
  refund
}

input PaymentCollectionWhereUniqueInput {
  id: ID
  cart: CartWhereUniqueInput
}

input PaymentCollectionWhereInput {
  AND: [PaymentCollectionWhereInput!]
  OR: [PaymentCollectionWhereInput!]
  NOT: [PaymentCollectionWhereInput!]
  id: IDFilter
  description: PaymentCollectionDescriptionTypeNullableFilter
  amount: IntFilter
  authorizedAmount: IntNullableFilter
  refundedAmount: IntNullableFilter
  paymentSessions: PaymentSessionManyRelationFilter
  payments: PaymentManyRelationFilter
  cart: CartWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PaymentCollectionDescriptionTypeNullableFilter {
  equals: PaymentCollectionDescriptionType
  in: [PaymentCollectionDescriptionType!]
  notIn: [PaymentCollectionDescriptionType!]
  not: PaymentCollectionDescriptionTypeNullableFilter
}

input PaymentSessionManyRelationFilter {
  every: PaymentSessionWhereInput
  some: PaymentSessionWhereInput
  none: PaymentSessionWhereInput
}

input PaymentCollectionOrderByInput {
  id: OrderDirection
  description: OrderDirection
  amount: OrderDirection
  authorizedAmount: OrderDirection
  refundedAmount: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PaymentCollectionUpdateInput {
  description: PaymentCollectionDescriptionType
  amount: Int
  authorizedAmount: Int
  refundedAmount: Int
  metadata: JSON
  paymentSessions: PaymentSessionRelateToManyForUpdateInput
  payments: PaymentRelateToManyForUpdateInput
  cart: CartRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentSessionRelateToManyForUpdateInput {
  disconnect: [PaymentSessionWhereUniqueInput!]
  set: [PaymentSessionWhereUniqueInput!]
  create: [PaymentSessionCreateInput!]
  connect: [PaymentSessionWhereUniqueInput!]
}

input PaymentCollectionUpdateArgs {
  where: PaymentCollectionWhereUniqueInput!
  data: PaymentCollectionUpdateInput!
}

input PaymentCollectionCreateInput {
  description: PaymentCollectionDescriptionType
  amount: Int
  authorizedAmount: Int
  refundedAmount: Int
  metadata: JSON
  paymentSessions: PaymentSessionRelateToManyForCreateInput
  payments: PaymentRelateToManyForCreateInput
  cart: CartRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentSessionRelateToManyForCreateInput {
  create: [PaymentSessionCreateInput!]
  connect: [PaymentSessionWhereUniqueInput!]
}

type PaymentProvider {
  id: ID!
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  createPaymentFunction: String
  capturePaymentFunction: String
  refundPaymentFunction: String
  getPaymentStatusFunction: String
  generatePaymentLinkFunction: String
  handleWebhookFunction: String
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  sessions(where: PaymentSessionWhereInput! = {}, orderBy: [PaymentSessionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentSessionWhereUniqueInput): [PaymentSession!]
  sessionsCount(where: PaymentSessionWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentProviderWhereUniqueInput {
  id: ID
  code: String
}

input PaymentProviderWhereInput {
  AND: [PaymentProviderWhereInput!]
  OR: [PaymentProviderWhereInput!]
  NOT: [PaymentProviderWhereInput!]
  id: IDFilter
  name: StringFilter
  code: StringFilter
  isInstalled: BooleanFilter
  createPaymentFunction: StringFilter
  capturePaymentFunction: StringFilter
  refundPaymentFunction: StringFilter
  getPaymentStatusFunction: StringFilter
  generatePaymentLinkFunction: StringFilter
  handleWebhookFunction: StringFilter
  regions: RegionManyRelationFilter
  sessions: PaymentSessionManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PaymentProviderOrderByInput {
  id: OrderDirection
  name: OrderDirection
  code: OrderDirection
  isInstalled: OrderDirection
  createPaymentFunction: OrderDirection
  capturePaymentFunction: OrderDirection
  refundPaymentFunction: OrderDirection
  getPaymentStatusFunction: OrderDirection
  generatePaymentLinkFunction: OrderDirection
  handleWebhookFunction: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PaymentProviderUpdateInput {
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  createPaymentFunction: String
  capturePaymentFunction: String
  refundPaymentFunction: String
  getPaymentStatusFunction: String
  generatePaymentLinkFunction: String
  handleWebhookFunction: String
  regions: RegionRelateToManyForUpdateInput
  sessions: PaymentSessionRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentProviderUpdateArgs {
  where: PaymentProviderWhereUniqueInput!
  data: PaymentProviderUpdateInput!
}

input PaymentProviderCreateInput {
  name: String
  code: String
  isInstalled: Boolean
  credentials: JSON
  metadata: JSON
  createPaymentFunction: String
  capturePaymentFunction: String
  refundPaymentFunction: String
  getPaymentStatusFunction: String
  generatePaymentLinkFunction: String
  handleWebhookFunction: String
  regions: RegionRelateToManyForCreateInput
  sessions: PaymentSessionRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type PaymentSession {
  id: ID!
  isSelected: Boolean
  isInitiated: Boolean
  amount: Int
  formattedAmount: String
  data: JSON
  idempotencyKey: String
  paymentCollection: PaymentCollection
  paymentProvider: PaymentProvider
  paymentAuthorizedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentSessionWhereUniqueInput {
  id: ID
}

input PaymentSessionWhereInput {
  AND: [PaymentSessionWhereInput!]
  OR: [PaymentSessionWhereInput!]
  NOT: [PaymentSessionWhereInput!]
  id: IDFilter
  isSelected: BooleanFilter
  isInitiated: BooleanFilter
  amount: IntFilter
  idempotencyKey: StringFilter
  paymentCollection: PaymentCollectionWhereInput
  paymentProvider: PaymentProviderWhereInput
  paymentAuthorizedAt: DateTimeNullableFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PaymentSessionOrderByInput {
  id: OrderDirection
  isSelected: OrderDirection
  isInitiated: OrderDirection
  amount: OrderDirection
  idempotencyKey: OrderDirection
  paymentAuthorizedAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PaymentSessionUpdateInput {
  isSelected: Boolean
  isInitiated: Boolean
  amount: Int
  data: JSON
  idempotencyKey: String
  paymentCollection: PaymentCollectionRelateToOneForUpdateInput
  paymentProvider: PaymentProviderRelateToOneForUpdateInput
  paymentAuthorizedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentProviderRelateToOneForUpdateInput {
  create: PaymentProviderCreateInput
  connect: PaymentProviderWhereUniqueInput
  disconnect: Boolean
}

input PaymentSessionUpdateArgs {
  where: PaymentSessionWhereUniqueInput!
  data: PaymentSessionUpdateInput!
}

input PaymentSessionCreateInput {
  isSelected: Boolean
  isInitiated: Boolean
  amount: Int
  data: JSON
  idempotencyKey: String
  paymentCollection: PaymentCollectionRelateToOneForCreateInput
  paymentProvider: PaymentProviderRelateToOneForCreateInput
  paymentAuthorizedAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
}

input PaymentProviderRelateToOneForCreateInput {
  create: PaymentProviderCreateInput
  connect: PaymentProviderWhereUniqueInput
}

type PriceList {
  id: ID!
  name: String
  description: String
  type: PriceListTypeType
  status: PriceListStatusType
  startsAt: DateTime
  endsAt: DateTime
  moneyAmounts(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  moneyAmountsCount(where: MoneyAmountWhereInput! = {}): Int
  customerGroups(where: CustomerGroupWhereInput! = {}, orderBy: [CustomerGroupOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomerGroupWhereUniqueInput): [CustomerGroup!]
  customerGroupsCount(where: CustomerGroupWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum PriceListTypeType {
  sale
  override
}

enum PriceListStatusType {
  active
  draft
}

input PriceListWhereUniqueInput {
  id: ID
}

input PriceListWhereInput {
  AND: [PriceListWhereInput!]
  OR: [PriceListWhereInput!]
  NOT: [PriceListWhereInput!]
  id: IDFilter
  name: StringFilter
  description: StringFilter
  type: PriceListTypeTypeNullableFilter
  status: PriceListStatusTypeNullableFilter
  startsAt: DateTimeNullableFilter
  endsAt: DateTimeNullableFilter
  moneyAmounts: MoneyAmountManyRelationFilter
  customerGroups: CustomerGroupManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PriceListTypeTypeNullableFilter {
  equals: PriceListTypeType
  in: [PriceListTypeType!]
  notIn: [PriceListTypeType!]
  not: PriceListTypeTypeNullableFilter
}

input PriceListStatusTypeNullableFilter {
  equals: PriceListStatusType
  in: [PriceListStatusType!]
  notIn: [PriceListStatusType!]
  not: PriceListStatusTypeNullableFilter
}

input PriceListOrderByInput {
  id: OrderDirection
  name: OrderDirection
  description: OrderDirection
  type: OrderDirection
  status: OrderDirection
  startsAt: OrderDirection
  endsAt: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PriceListUpdateInput {
  name: String
  description: String
  type: PriceListTypeType
  status: PriceListStatusType
  startsAt: DateTime
  endsAt: DateTime
  moneyAmounts: MoneyAmountRelateToManyForUpdateInput
  customerGroups: CustomerGroupRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PriceListUpdateArgs {
  where: PriceListWhereUniqueInput!
  data: PriceListUpdateInput!
}

input PriceListCreateInput {
  name: String
  description: String
  type: PriceListTypeType
  status: PriceListStatusType
  startsAt: DateTime
  endsAt: DateTime
  moneyAmounts: MoneyAmountRelateToManyForCreateInput
  customerGroups: CustomerGroupRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type PriceRule {
  id: ID!
  type: PriceRuleTypeType
  value: Float
  priority: Int
  ruleAttribute: String
  ruleValue: String
  moneyAmounts(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  moneyAmountsCount(where: MoneyAmountWhereInput! = {}): Int
  priceSet: PriceSet
  createdAt: DateTime
  updatedAt: DateTime
}

enum PriceRuleTypeType {
  fixed
  percentage
}

input PriceRuleWhereUniqueInput {
  id: ID
}

input PriceRuleWhereInput {
  AND: [PriceRuleWhereInput!]
  OR: [PriceRuleWhereInput!]
  NOT: [PriceRuleWhereInput!]
  id: IDFilter
  type: PriceRuleTypeTypeNullableFilter
  value: FloatFilter
  priority: IntNullableFilter
  ruleAttribute: StringFilter
  ruleValue: StringFilter
  moneyAmounts: MoneyAmountManyRelationFilter
  priceSet: PriceSetWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PriceRuleTypeTypeNullableFilter {
  equals: PriceRuleTypeType
  in: [PriceRuleTypeType!]
  notIn: [PriceRuleTypeType!]
  not: PriceRuleTypeTypeNullableFilter
}

input PriceRuleOrderByInput {
  id: OrderDirection
  type: OrderDirection
  value: OrderDirection
  priority: OrderDirection
  ruleAttribute: OrderDirection
  ruleValue: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PriceRuleUpdateInput {
  type: PriceRuleTypeType
  value: Float
  priority: Int
  ruleAttribute: String
  ruleValue: String
  moneyAmounts: MoneyAmountRelateToManyForUpdateInput
  priceSet: PriceSetRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PriceRuleUpdateArgs {
  where: PriceRuleWhereUniqueInput!
  data: PriceRuleUpdateInput!
}

input PriceRuleCreateInput {
  type: PriceRuleTypeType
  value: Float
  priority: Int
  ruleAttribute: String
  ruleValue: String
  moneyAmounts: MoneyAmountRelateToManyForCreateInput
  priceSet: PriceSetRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type PriceSet {
  id: ID!
  prices(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  pricesCount(where: MoneyAmountWhereInput! = {}): Int
  priceRules(where: PriceRuleWhereInput! = {}, orderBy: [PriceRuleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceRuleWhereUniqueInput): [PriceRule!]
  priceRulesCount(where: PriceRuleWhereInput! = {}): Int
  ruleTypes(where: RuleTypeWhereInput! = {}, orderBy: [RuleTypeOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RuleTypeWhereUniqueInput): [RuleType!]
  ruleTypesCount(where: RuleTypeWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input PriceSetWhereUniqueInput {
  id: ID
}

input PriceSetWhereInput {
  AND: [PriceSetWhereInput!]
  OR: [PriceSetWhereInput!]
  NOT: [PriceSetWhereInput!]
  id: IDFilter
  prices: MoneyAmountManyRelationFilter
  priceRules: PriceRuleManyRelationFilter
  ruleTypes: RuleTypeManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input RuleTypeManyRelationFilter {
  every: RuleTypeWhereInput
  some: RuleTypeWhereInput
  none: RuleTypeWhereInput
}

input PriceSetOrderByInput {
  id: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input PriceSetUpdateInput {
  prices: MoneyAmountRelateToManyForUpdateInput
  priceRules: PriceRuleRelateToManyForUpdateInput
  ruleTypes: RuleTypeRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RuleTypeRelateToManyForUpdateInput {
  disconnect: [RuleTypeWhereUniqueInput!]
  set: [RuleTypeWhereUniqueInput!]
  create: [RuleTypeCreateInput!]
  connect: [RuleTypeWhereUniqueInput!]
}

input PriceSetUpdateArgs {
  where: PriceSetWhereUniqueInput!
  data: PriceSetUpdateInput!
}

input PriceSetCreateInput {
  prices: MoneyAmountRelateToManyForCreateInput
  priceRules: PriceRuleRelateToManyForCreateInput
  ruleTypes: RuleTypeRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RuleTypeRelateToManyForCreateInput {
  create: [RuleTypeCreateInput!]
  connect: [RuleTypeWhereUniqueInput!]
}

type Product {
  id: ID!
  title: String
  description: Product_description_Document
  handle: String
  subtitle: String
  isGiftcard: Boolean
  thumbnail: String
  dimensionsRange: JSON
  defaultDimensions: JSON
  metadata: JSON
  discountable: Boolean
  status: ProductStatusType
  externalId: String
  productCollections(where: ProductCollectionWhereInput! = {}, orderBy: [ProductCollectionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCollectionWhereUniqueInput): [ProductCollection!]
  productCollectionsCount(where: ProductCollectionWhereInput! = {}): Int
  productCategories(where: ProductCategoryWhereInput! = {}, orderBy: [ProductCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCategoryWhereUniqueInput): [ProductCategory!]
  productCategoriesCount(where: ProductCategoryWhereInput! = {}): Int
  shippingProfile: ShippingProfile
  productType: ProductType
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  discountRules(where: DiscountRuleWhereInput! = {}, orderBy: [DiscountRuleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountRuleWhereUniqueInput): [DiscountRule!]
  discountRulesCount(where: DiscountRuleWhereInput! = {}): Int
  productImages(where: ProductImageWhereInput! = {}, orderBy: [ProductImageOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductImageWhereUniqueInput): [ProductImage!]
  productImagesCount(where: ProductImageWhereInput! = {}): Int
  productOptions(where: ProductOptionWhereInput! = {}, orderBy: [ProductOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductOptionWhereUniqueInput): [ProductOption!]
  productOptionsCount(where: ProductOptionWhereInput! = {}): Int
  productTags(where: ProductTagWhereInput! = {}, orderBy: [ProductTagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTagWhereUniqueInput): [ProductTag!]
  productTagsCount(where: ProductTagWhereInput! = {}): Int
  taxRates(where: TaxRateWhereInput! = {}, orderBy: [TaxRateOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxRateWhereUniqueInput): [TaxRate!]
  taxRatesCount(where: TaxRateWhereInput! = {}): Int
  productVariants(where: ProductVariantWhereInput! = {}, orderBy: [ProductVariantOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductVariantWhereUniqueInput): [ProductVariant!]
  productVariantsCount(where: ProductVariantWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

type Product_description_Document {
  document(hydrateRelationships: Boolean! = false): JSON!
}

enum ProductStatusType {
  draft
  proposed
  published
  rejected
}

input ProductWhereUniqueInput {
  id: ID
  handle: String
}

input ProductWhereInput {
  AND: [ProductWhereInput!]
  OR: [ProductWhereInput!]
  NOT: [ProductWhereInput!]
  id: IDFilter
  title: StringFilter
  handle: StringFilter
  subtitle: StringFilter
  isGiftcard: BooleanFilter
  discountable: BooleanFilter
  status: ProductStatusTypeNullableFilter
  externalId: StringFilter
  productCollections: ProductCollectionManyRelationFilter
  productCategories: ProductCategoryManyRelationFilter
  shippingProfile: ShippingProfileWhereInput
  productType: ProductTypeWhereInput
  discountConditions: DiscountConditionManyRelationFilter
  discountRules: DiscountRuleManyRelationFilter
  productImages: ProductImageManyRelationFilter
  productOptions: ProductOptionManyRelationFilter
  productTags: ProductTagManyRelationFilter
  taxRates: TaxRateManyRelationFilter
  productVariants: ProductVariantManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductStatusTypeNullableFilter {
  equals: ProductStatusType
  in: [ProductStatusType!]
  notIn: [ProductStatusType!]
  not: ProductStatusTypeNullableFilter
}

input DiscountRuleManyRelationFilter {
  every: DiscountRuleWhereInput
  some: DiscountRuleWhereInput
  none: DiscountRuleWhereInput
}

input ProductImageManyRelationFilter {
  every: ProductImageWhereInput
  some: ProductImageWhereInput
  none: ProductImageWhereInput
}

input ProductOptionManyRelationFilter {
  every: ProductOptionWhereInput
  some: ProductOptionWhereInput
  none: ProductOptionWhereInput
}

input TaxRateManyRelationFilter {
  every: TaxRateWhereInput
  some: TaxRateWhereInput
  none: TaxRateWhereInput
}

input ProductOrderByInput {
  id: OrderDirection
  title: OrderDirection
  handle: OrderDirection
  subtitle: OrderDirection
  isGiftcard: OrderDirection
  discountable: OrderDirection
  status: OrderDirection
  externalId: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductUpdateInput {
  title: String
  description: JSON
  handle: String
  subtitle: String
  isGiftcard: Boolean
  metadata: JSON
  discountable: Boolean
  status: ProductStatusType
  externalId: String
  productCollections: ProductCollectionRelateToManyForUpdateInput
  productCategories: ProductCategoryRelateToManyForUpdateInput
  shippingProfile: ShippingProfileRelateToOneForUpdateInput
  productType: ProductTypeRelateToOneForUpdateInput
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  discountRules: DiscountRuleRelateToManyForUpdateInput
  productImages: ProductImageRelateToManyForUpdateInput
  productOptions: ProductOptionRelateToManyForUpdateInput
  productTags: ProductTagRelateToManyForUpdateInput
  taxRates: TaxRateRelateToManyForUpdateInput
  productVariants: ProductVariantRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProfileRelateToOneForUpdateInput {
  create: ShippingProfileCreateInput
  connect: ShippingProfileWhereUniqueInput
  disconnect: Boolean
}

input ProductTypeRelateToOneForUpdateInput {
  create: ProductTypeCreateInput
  connect: ProductTypeWhereUniqueInput
  disconnect: Boolean
}

input DiscountRuleRelateToManyForUpdateInput {
  disconnect: [DiscountRuleWhereUniqueInput!]
  set: [DiscountRuleWhereUniqueInput!]
  create: [DiscountRuleCreateInput!]
  connect: [DiscountRuleWhereUniqueInput!]
}

input ProductImageRelateToManyForUpdateInput {
  disconnect: [ProductImageWhereUniqueInput!]
  set: [ProductImageWhereUniqueInput!]
  create: [ProductImageCreateInput!]
  connect: [ProductImageWhereUniqueInput!]
}

input ProductOptionRelateToManyForUpdateInput {
  disconnect: [ProductOptionWhereUniqueInput!]
  set: [ProductOptionWhereUniqueInput!]
  create: [ProductOptionCreateInput!]
  connect: [ProductOptionWhereUniqueInput!]
}

input TaxRateRelateToManyForUpdateInput {
  disconnect: [TaxRateWhereUniqueInput!]
  set: [TaxRateWhereUniqueInput!]
  create: [TaxRateCreateInput!]
  connect: [TaxRateWhereUniqueInput!]
}

input ProductUpdateArgs {
  where: ProductWhereUniqueInput!
  data: ProductUpdateInput!
}

input ProductCreateInput {
  title: String
  description: JSON
  handle: String
  subtitle: String
  isGiftcard: Boolean
  metadata: JSON
  discountable: Boolean
  status: ProductStatusType
  externalId: String
  productCollections: ProductCollectionRelateToManyForCreateInput
  productCategories: ProductCategoryRelateToManyForCreateInput
  shippingProfile: ShippingProfileRelateToOneForCreateInput
  productType: ProductTypeRelateToOneForCreateInput
  discountConditions: DiscountConditionRelateToManyForCreateInput
  discountRules: DiscountRuleRelateToManyForCreateInput
  productImages: ProductImageRelateToManyForCreateInput
  productOptions: ProductOptionRelateToManyForCreateInput
  productTags: ProductTagRelateToManyForCreateInput
  taxRates: TaxRateRelateToManyForCreateInput
  productVariants: ProductVariantRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProfileRelateToOneForCreateInput {
  create: ShippingProfileCreateInput
  connect: ShippingProfileWhereUniqueInput
}

input ProductTypeRelateToOneForCreateInput {
  create: ProductTypeCreateInput
  connect: ProductTypeWhereUniqueInput
}

input DiscountRuleRelateToManyForCreateInput {
  create: [DiscountRuleCreateInput!]
  connect: [DiscountRuleWhereUniqueInput!]
}

input ProductImageRelateToManyForCreateInput {
  create: [ProductImageCreateInput!]
  connect: [ProductImageWhereUniqueInput!]
}

input ProductOptionRelateToManyForCreateInput {
  create: [ProductOptionCreateInput!]
  connect: [ProductOptionWhereUniqueInput!]
}

input TaxRateRelateToManyForCreateInput {
  create: [TaxRateCreateInput!]
  connect: [TaxRateWhereUniqueInput!]
}

type ProductCategory {
  id: ID!
  title: String
  handle: String
  metadata: JSON
  isInternal: Boolean
  isActive: Boolean
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  parentCategory: ProductCategory
  categoryChildren(where: ProductCategoryWhereInput! = {}, orderBy: [ProductCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCategoryWhereUniqueInput): [ProductCategory!]
  categoryChildrenCount(where: ProductCategoryWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductCategoryWhereUniqueInput {
  id: ID
  handle: String
}

input ProductCategoryWhereInput {
  AND: [ProductCategoryWhereInput!]
  OR: [ProductCategoryWhereInput!]
  NOT: [ProductCategoryWhereInput!]
  id: IDFilter
  title: StringFilter
  handle: StringFilter
  isInternal: BooleanFilter
  isActive: BooleanFilter
  discountConditions: DiscountConditionManyRelationFilter
  products: ProductManyRelationFilter
  parentCategory: ProductCategoryWhereInput
  categoryChildren: ProductCategoryManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductCategoryOrderByInput {
  id: OrderDirection
  title: OrderDirection
  handle: OrderDirection
  isInternal: OrderDirection
  isActive: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductCategoryUpdateInput {
  title: String
  handle: String
  metadata: JSON
  isInternal: Boolean
  isActive: Boolean
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  parentCategory: ProductCategoryRelateToOneForUpdateInput
  categoryChildren: ProductCategoryRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductCategoryRelateToOneForUpdateInput {
  create: ProductCategoryCreateInput
  connect: ProductCategoryWhereUniqueInput
  disconnect: Boolean
}

input ProductCategoryUpdateArgs {
  where: ProductCategoryWhereUniqueInput!
  data: ProductCategoryUpdateInput!
}

input ProductCategoryCreateInput {
  title: String
  handle: String
  metadata: JSON
  isInternal: Boolean
  isActive: Boolean
  discountConditions: DiscountConditionRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  parentCategory: ProductCategoryRelateToOneForCreateInput
  categoryChildren: ProductCategoryRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductCategoryRelateToOneForCreateInput {
  create: ProductCategoryCreateInput
  connect: ProductCategoryWhereUniqueInput
}

type ProductCollection {
  id: ID!
  title: String
  handle: String
  metadata: JSON
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductCollectionWhereUniqueInput {
  id: ID
  handle: String
}

input ProductCollectionWhereInput {
  AND: [ProductCollectionWhereInput!]
  OR: [ProductCollectionWhereInput!]
  NOT: [ProductCollectionWhereInput!]
  id: IDFilter
  title: StringFilter
  handle: StringFilter
  discountConditions: DiscountConditionManyRelationFilter
  products: ProductManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductCollectionOrderByInput {
  id: OrderDirection
  title: OrderDirection
  handle: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductCollectionUpdateInput {
  title: String
  handle: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductCollectionUpdateArgs {
  where: ProductCollectionWhereUniqueInput!
  data: ProductCollectionUpdateInput!
}

input ProductCollectionCreateInput {
  title: String
  handle: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ProductImage {
  id: ID!
  image: ImageFieldOutput
  imagePath: String
  altText: String
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductImageWhereUniqueInput {
  id: ID
}

input ProductImageWhereInput {
  AND: [ProductImageWhereInput!]
  OR: [ProductImageWhereInput!]
  NOT: [ProductImageWhereInput!]
  id: IDFilter
  imagePath: StringFilter
  altText: StringFilter
  products: ProductManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductImageOrderByInput {
  id: OrderDirection
  imagePath: OrderDirection
  altText: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductImageUpdateInput {
  image: ImageFieldInput
  imagePath: String
  altText: String
  products: ProductRelateToManyForUpdateInput
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductImageUpdateArgs {
  where: ProductImageWhereUniqueInput!
  data: ProductImageUpdateInput!
}

input ProductImageCreateInput {
  image: ImageFieldInput
  imagePath: String
  altText: String
  products: ProductRelateToManyForCreateInput
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

type ProductOption {
  id: ID!
  title: String
  metadata: JSON
  product: Product
  productOptionValues(where: ProductOptionValueWhereInput! = {}, orderBy: [ProductOptionValueOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductOptionValueWhereUniqueInput): [ProductOptionValue!]
  productOptionValuesCount(where: ProductOptionValueWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductOptionWhereUniqueInput {
  id: ID
}

input ProductOptionWhereInput {
  AND: [ProductOptionWhereInput!]
  OR: [ProductOptionWhereInput!]
  NOT: [ProductOptionWhereInput!]
  id: IDFilter
  title: StringFilter
  product: ProductWhereInput
  productOptionValues: ProductOptionValueManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductOptionValueManyRelationFilter {
  every: ProductOptionValueWhereInput
  some: ProductOptionValueWhereInput
  none: ProductOptionValueWhereInput
}

input ProductOptionOrderByInput {
  id: OrderDirection
  title: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductOptionUpdateInput {
  title: String
  metadata: JSON
  product: ProductRelateToOneForUpdateInput
  productOptionValues: ProductOptionValueRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductRelateToOneForUpdateInput {
  create: ProductCreateInput
  connect: ProductWhereUniqueInput
  disconnect: Boolean
}

input ProductOptionValueRelateToManyForUpdateInput {
  disconnect: [ProductOptionValueWhereUniqueInput!]
  set: [ProductOptionValueWhereUniqueInput!]
  create: [ProductOptionValueCreateInput!]
  connect: [ProductOptionValueWhereUniqueInput!]
}

input ProductOptionUpdateArgs {
  where: ProductOptionWhereUniqueInput!
  data: ProductOptionUpdateInput!
}

input ProductOptionCreateInput {
  title: String
  metadata: JSON
  product: ProductRelateToOneForCreateInput
  productOptionValues: ProductOptionValueRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductRelateToOneForCreateInput {
  create: ProductCreateInput
  connect: ProductWhereUniqueInput
}

input ProductOptionValueRelateToManyForCreateInput {
  create: [ProductOptionValueCreateInput!]
  connect: [ProductOptionValueWhereUniqueInput!]
}

type ProductOptionValue {
  id: ID!
  value: String
  metadata: JSON
  productVariants(where: ProductVariantWhereInput! = {}, orderBy: [ProductVariantOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductVariantWhereUniqueInput): [ProductVariant!]
  productVariantsCount(where: ProductVariantWhereInput! = {}): Int
  productOption: ProductOption
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductOptionValueWhereUniqueInput {
  id: ID
}

input ProductOptionValueWhereInput {
  AND: [ProductOptionValueWhereInput!]
  OR: [ProductOptionValueWhereInput!]
  NOT: [ProductOptionValueWhereInput!]
  id: IDFilter
  value: StringFilter
  productVariants: ProductVariantManyRelationFilter
  productOption: ProductOptionWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductOptionValueOrderByInput {
  id: OrderDirection
  value: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductOptionValueUpdateInput {
  value: String
  metadata: JSON
  productVariants: ProductVariantRelateToManyForUpdateInput
  productOption: ProductOptionRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductOptionRelateToOneForUpdateInput {
  create: ProductOptionCreateInput
  connect: ProductOptionWhereUniqueInput
  disconnect: Boolean
}

input ProductOptionValueUpdateArgs {
  where: ProductOptionValueWhereUniqueInput!
  data: ProductOptionValueUpdateInput!
}

input ProductOptionValueCreateInput {
  value: String
  metadata: JSON
  productVariants: ProductVariantRelateToManyForCreateInput
  productOption: ProductOptionRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductOptionRelateToOneForCreateInput {
  create: ProductOptionCreateInput
  connect: ProductOptionWhereUniqueInput
}

type ProductTag {
  id: ID!
  value: String
  metadata: JSON
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductTagWhereUniqueInput {
  id: ID
}

input ProductTagWhereInput {
  AND: [ProductTagWhereInput!]
  OR: [ProductTagWhereInput!]
  NOT: [ProductTagWhereInput!]
  id: IDFilter
  value: StringFilter
  discountConditions: DiscountConditionManyRelationFilter
  products: ProductManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductTagOrderByInput {
  id: OrderDirection
  value: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductTagUpdateInput {
  value: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductTagUpdateArgs {
  where: ProductTagWhereUniqueInput!
  data: ProductTagUpdateInput!
}

input ProductTagCreateInput {
  value: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ProductType {
  id: ID!
  value: String
  metadata: JSON
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  taxRates(where: TaxRateWhereInput! = {}, orderBy: [TaxRateOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxRateWhereUniqueInput): [TaxRate!]
  taxRatesCount(where: TaxRateWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductTypeWhereUniqueInput {
  id: ID
}

input ProductTypeWhereInput {
  AND: [ProductTypeWhereInput!]
  OR: [ProductTypeWhereInput!]
  NOT: [ProductTypeWhereInput!]
  id: IDFilter
  value: StringFilter
  discountConditions: DiscountConditionManyRelationFilter
  products: ProductManyRelationFilter
  taxRates: TaxRateManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ProductTypeOrderByInput {
  id: OrderDirection
  value: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductTypeUpdateInput {
  value: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForUpdateInput
  products: ProductRelateToManyForUpdateInput
  taxRates: TaxRateRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductTypeUpdateArgs {
  where: ProductTypeWhereUniqueInput!
  data: ProductTypeUpdateInput!
}

input ProductTypeCreateInput {
  value: String
  metadata: JSON
  discountConditions: DiscountConditionRelateToManyForCreateInput
  products: ProductRelateToManyForCreateInput
  taxRates: TaxRateRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ProductVariant {
  id: ID!
  fullTitle: String
  title: String
  sku: String
  barcode: String
  ean: String
  upc: String
  inventoryQuantity: Int
  allowBackorder: Boolean
  manageInventory: Boolean
  hsCode: String
  originCountry: String
  midCode: String
  material: String
  metadata: JSON
  variantRank: Int
  product: Product
  claimItems(where: ClaimItemWhereInput! = {}, orderBy: [ClaimItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimItemWhereUniqueInput): [ClaimItem!]
  claimItemsCount(where: ClaimItemWhereInput! = {}): Int
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  prices(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  pricesCount(where: MoneyAmountWhereInput! = {}): Int
  productOptionValues(where: ProductOptionValueWhereInput! = {}, orderBy: [ProductOptionValueOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductOptionValueWhereUniqueInput): [ProductOptionValue!]
  productOptionValuesCount(where: ProductOptionValueWhereInput! = {}): Int
  location: Location
  stockMovements(where: StockMovementWhereInput! = {}, orderBy: [StockMovementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: StockMovementWhereUniqueInput): [StockMovement!]
  stockMovementsCount(where: StockMovementWhereInput! = {}): Int
  measurements(where: MeasurementWhereInput! = {}, orderBy: [MeasurementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MeasurementWhereUniqueInput): [Measurement!]
  measurementsCount(where: MeasurementWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ProductVariantWhereUniqueInput {
  id: ID
}

input ProductVariantWhereInput {
  AND: [ProductVariantWhereInput!]
  OR: [ProductVariantWhereInput!]
  NOT: [ProductVariantWhereInput!]
  id: IDFilter
  title: StringFilter
  sku: StringFilter
  barcode: StringFilter
  ean: StringFilter
  upc: StringFilter
  inventoryQuantity: IntFilter
  allowBackorder: BooleanFilter
  manageInventory: BooleanFilter
  hsCode: StringFilter
  originCountry: StringFilter
  midCode: StringFilter
  material: StringFilter
  variantRank: IntNullableFilter
  product: ProductWhereInput
  claimItems: ClaimItemManyRelationFilter
  lineItems: LineItemManyRelationFilter
  prices: MoneyAmountManyRelationFilter
  productOptionValues: ProductOptionValueManyRelationFilter
  location: LocationWhereInput
  stockMovements: StockMovementManyRelationFilter
  measurements: MeasurementManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input StockMovementManyRelationFilter {
  every: StockMovementWhereInput
  some: StockMovementWhereInput
  none: StockMovementWhereInput
}

input MeasurementManyRelationFilter {
  every: MeasurementWhereInput
  some: MeasurementWhereInput
  none: MeasurementWhereInput
}

input ProductVariantOrderByInput {
  id: OrderDirection
  title: OrderDirection
  sku: OrderDirection
  barcode: OrderDirection
  ean: OrderDirection
  upc: OrderDirection
  inventoryQuantity: OrderDirection
  allowBackorder: OrderDirection
  manageInventory: OrderDirection
  hsCode: OrderDirection
  originCountry: OrderDirection
  midCode: OrderDirection
  material: OrderDirection
  variantRank: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ProductVariantUpdateInput {
  title: String
  sku: String
  barcode: String
  ean: String
  upc: String
  inventoryQuantity: Int
  allowBackorder: Boolean
  manageInventory: Boolean
  hsCode: String
  originCountry: String
  midCode: String
  material: String
  metadata: JSON
  variantRank: Int
  product: ProductRelateToOneForUpdateInput
  claimItems: ClaimItemRelateToManyForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  prices: MoneyAmountRelateToManyForUpdateInput
  productOptionValues: ProductOptionValueRelateToManyForUpdateInput
  location: LocationRelateToOneForUpdateInput
  stockMovements: StockMovementRelateToManyForUpdateInput
  measurements: MeasurementRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LocationRelateToOneForUpdateInput {
  create: LocationCreateInput
  connect: LocationWhereUniqueInput
  disconnect: Boolean
}

input StockMovementRelateToManyForUpdateInput {
  disconnect: [StockMovementWhereUniqueInput!]
  set: [StockMovementWhereUniqueInput!]
  create: [StockMovementCreateInput!]
  connect: [StockMovementWhereUniqueInput!]
}

input MeasurementRelateToManyForUpdateInput {
  disconnect: [MeasurementWhereUniqueInput!]
  set: [MeasurementWhereUniqueInput!]
  create: [MeasurementCreateInput!]
  connect: [MeasurementWhereUniqueInput!]
}

input ProductVariantUpdateArgs {
  where: ProductVariantWhereUniqueInput!
  data: ProductVariantUpdateInput!
}

input ProductVariantCreateInput {
  title: String
  sku: String
  barcode: String
  ean: String
  upc: String
  inventoryQuantity: Int
  allowBackorder: Boolean
  manageInventory: Boolean
  hsCode: String
  originCountry: String
  midCode: String
  material: String
  metadata: JSON
  variantRank: Int
  product: ProductRelateToOneForCreateInput
  claimItems: ClaimItemRelateToManyForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  prices: MoneyAmountRelateToManyForCreateInput
  productOptionValues: ProductOptionValueRelateToManyForCreateInput
  location: LocationRelateToOneForCreateInput
  stockMovements: StockMovementRelateToManyForCreateInput
  measurements: MeasurementRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input LocationRelateToOneForCreateInput {
  create: LocationCreateInput
  connect: LocationWhereUniqueInput
}

input StockMovementRelateToManyForCreateInput {
  create: [StockMovementCreateInput!]
  connect: [StockMovementWhereUniqueInput!]
}

input MeasurementRelateToManyForCreateInput {
  create: [MeasurementCreateInput!]
  connect: [MeasurementWhereUniqueInput!]
}

type Refund {
  id: ID!
  amount: Int
  note: String
  reason: RefundReasonType
  metadata: JSON
  idempotencyKey: String
  payment: Payment
  createdAt: DateTime
  updatedAt: DateTime
}

enum RefundReasonType {
  discount
  return
  swap
  claim
  other
}

input RefundWhereUniqueInput {
  id: ID
}

input RefundWhereInput {
  AND: [RefundWhereInput!]
  OR: [RefundWhereInput!]
  NOT: [RefundWhereInput!]
  id: IDFilter
  amount: IntFilter
  note: StringFilter
  reason: RefundReasonTypeNullableFilter
  idempotencyKey: StringFilter
  payment: PaymentWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input RefundReasonTypeNullableFilter {
  equals: RefundReasonType
  in: [RefundReasonType!]
  notIn: [RefundReasonType!]
  not: RefundReasonTypeNullableFilter
}

input RefundOrderByInput {
  id: OrderDirection
  amount: OrderDirection
  note: OrderDirection
  reason: OrderDirection
  idempotencyKey: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input RefundUpdateInput {
  amount: Int
  note: String
  reason: RefundReasonType
  metadata: JSON
  idempotencyKey: String
  payment: PaymentRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RefundUpdateArgs {
  where: RefundWhereUniqueInput!
  data: RefundUpdateInput!
}

input RefundCreateInput {
  amount: Int
  note: String
  reason: RefundReasonType
  metadata: JSON
  idempotencyKey: String
  payment: PaymentRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Region {
  id: ID!
  code: String
  name: String
  taxRate: Float
  taxCode: String
  metadata: JSON
  giftCardsTaxable: Boolean
  automaticTaxes: Boolean
  currency: Currency
  carts(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsCount(where: CartWhereInput! = {}): Int
  countries(where: CountryWhereInput! = {}, orderBy: [CountryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CountryWhereUniqueInput): [Country!]
  countriesCount(where: CountryWhereInput! = {}): Int
  discounts(where: DiscountWhereInput! = {}, orderBy: [DiscountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountWhereUniqueInput): [Discount!]
  discountsCount(where: DiscountWhereInput! = {}): Int
  giftCards(where: GiftCardWhereInput! = {}, orderBy: [GiftCardOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardWhereUniqueInput): [GiftCard!]
  giftCardsCount(where: GiftCardWhereInput! = {}): Int
  moneyAmounts(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  moneyAmountsCount(where: MoneyAmountWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  taxProvider: TaxProvider
  fulfillmentProviders(where: FulfillmentProviderWhereInput! = {}, orderBy: [FulfillmentProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentProviderWhereUniqueInput): [FulfillmentProvider!]
  fulfillmentProvidersCount(where: FulfillmentProviderWhereInput! = {}): Int
  paymentProviders(where: PaymentProviderWhereInput! = {}, orderBy: [PaymentProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentProviderWhereUniqueInput): [PaymentProvider!]
  paymentProvidersCount(where: PaymentProviderWhereInput! = {}): Int
  shippingOptions(where: ShippingOptionWhereInput! = {}, orderBy: [ShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionWhereUniqueInput): [ShippingOption!]
  shippingOptionsCount(where: ShippingOptionWhereInput! = {}): Int
  taxRates(where: TaxRateWhereInput! = {}, orderBy: [TaxRateOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxRateWhereUniqueInput): [TaxRate!]
  taxRatesCount(where: TaxRateWhereInput! = {}): Int
  shippingProviders(where: ShippingProviderWhereInput! = {}, orderBy: [ShippingProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingProviderWhereUniqueInput): [ShippingProvider!]
  shippingProvidersCount(where: ShippingProviderWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input RegionWhereUniqueInput {
  id: ID
  code: String
}

input RegionWhereInput {
  AND: [RegionWhereInput!]
  OR: [RegionWhereInput!]
  NOT: [RegionWhereInput!]
  id: IDFilter
  code: StringFilter
  name: StringFilter
  taxRate: FloatFilter
  taxCode: StringFilter
  giftCardsTaxable: BooleanFilter
  automaticTaxes: BooleanFilter
  currency: CurrencyWhereInput
  carts: CartManyRelationFilter
  countries: CountryManyRelationFilter
  discounts: DiscountManyRelationFilter
  giftCards: GiftCardManyRelationFilter
  moneyAmounts: MoneyAmountManyRelationFilter
  orders: OrderManyRelationFilter
  taxProvider: TaxProviderWhereInput
  fulfillmentProviders: FulfillmentProviderManyRelationFilter
  paymentProviders: PaymentProviderManyRelationFilter
  shippingOptions: ShippingOptionManyRelationFilter
  taxRates: TaxRateManyRelationFilter
  shippingProviders: ShippingProviderManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CountryManyRelationFilter {
  every: CountryWhereInput
  some: CountryWhereInput
  none: CountryWhereInput
}

input FulfillmentProviderManyRelationFilter {
  every: FulfillmentProviderWhereInput
  some: FulfillmentProviderWhereInput
  none: FulfillmentProviderWhereInput
}

input PaymentProviderManyRelationFilter {
  every: PaymentProviderWhereInput
  some: PaymentProviderWhereInput
  none: PaymentProviderWhereInput
}

input RegionOrderByInput {
  id: OrderDirection
  code: OrderDirection
  name: OrderDirection
  taxRate: OrderDirection
  taxCode: OrderDirection
  giftCardsTaxable: OrderDirection
  automaticTaxes: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input RegionUpdateInput {
  code: String
  name: String
  taxRate: Float
  taxCode: String
  metadata: JSON
  giftCardsTaxable: Boolean
  automaticTaxes: Boolean
  currency: CurrencyRelateToOneForUpdateInput
  carts: CartRelateToManyForUpdateInput
  countries: CountryRelateToManyForUpdateInput
  discounts: DiscountRelateToManyForUpdateInput
  giftCards: GiftCardRelateToManyForUpdateInput
  moneyAmounts: MoneyAmountRelateToManyForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  taxProvider: TaxProviderRelateToOneForUpdateInput
  fulfillmentProviders: FulfillmentProviderRelateToManyForUpdateInput
  paymentProviders: PaymentProviderRelateToManyForUpdateInput
  shippingOptions: ShippingOptionRelateToManyForUpdateInput
  taxRates: TaxRateRelateToManyForUpdateInput
  shippingProviders: ShippingProviderRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryRelateToManyForUpdateInput {
  disconnect: [CountryWhereUniqueInput!]
  set: [CountryWhereUniqueInput!]
  create: [CountryCreateInput!]
  connect: [CountryWhereUniqueInput!]
}

input TaxProviderRelateToOneForUpdateInput {
  create: TaxProviderCreateInput
  connect: TaxProviderWhereUniqueInput
  disconnect: Boolean
}

input FulfillmentProviderRelateToManyForUpdateInput {
  disconnect: [FulfillmentProviderWhereUniqueInput!]
  set: [FulfillmentProviderWhereUniqueInput!]
  create: [FulfillmentProviderCreateInput!]
  connect: [FulfillmentProviderWhereUniqueInput!]
}

input PaymentProviderRelateToManyForUpdateInput {
  disconnect: [PaymentProviderWhereUniqueInput!]
  set: [PaymentProviderWhereUniqueInput!]
  create: [PaymentProviderCreateInput!]
  connect: [PaymentProviderWhereUniqueInput!]
}

input RegionUpdateArgs {
  where: RegionWhereUniqueInput!
  data: RegionUpdateInput!
}

input RegionCreateInput {
  code: String
  name: String
  taxRate: Float
  taxCode: String
  metadata: JSON
  giftCardsTaxable: Boolean
  automaticTaxes: Boolean
  currency: CurrencyRelateToOneForCreateInput
  carts: CartRelateToManyForCreateInput
  countries: CountryRelateToManyForCreateInput
  discounts: DiscountRelateToManyForCreateInput
  giftCards: GiftCardRelateToManyForCreateInput
  moneyAmounts: MoneyAmountRelateToManyForCreateInput
  orders: OrderRelateToManyForCreateInput
  taxProvider: TaxProviderRelateToOneForCreateInput
  fulfillmentProviders: FulfillmentProviderRelateToManyForCreateInput
  paymentProviders: PaymentProviderRelateToManyForCreateInput
  shippingOptions: ShippingOptionRelateToManyForCreateInput
  taxRates: TaxRateRelateToManyForCreateInput
  shippingProviders: ShippingProviderRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CountryRelateToManyForCreateInput {
  create: [CountryCreateInput!]
  connect: [CountryWhereUniqueInput!]
}

input TaxProviderRelateToOneForCreateInput {
  create: TaxProviderCreateInput
  connect: TaxProviderWhereUniqueInput
}

input FulfillmentProviderRelateToManyForCreateInput {
  create: [FulfillmentProviderCreateInput!]
  connect: [FulfillmentProviderWhereUniqueInput!]
}

input PaymentProviderRelateToManyForCreateInput {
  create: [PaymentProviderCreateInput!]
  connect: [PaymentProviderWhereUniqueInput!]
}

type Return {
  id: ID!
  status: ReturnStatusType
  shippingData: JSON
  refundAmount: Int
  receivedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  claimOrder: ClaimOrder
  swap: Swap
  order: Order
  returnItems(where: ReturnItemWhereInput! = {}, orderBy: [ReturnItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnItemWhereUniqueInput): [ReturnItem!]
  returnItemsCount(where: ReturnItemWhereInput! = {}): Int
  shippingMethod: ShippingMethod
  createdAt: DateTime
  updatedAt: DateTime
}

enum ReturnStatusType {
  requested
  received
  requires_action
  canceled
}

input ReturnWhereUniqueInput {
  id: ID
  claimOrder: ClaimOrderWhereUniqueInput
  swap: SwapWhereUniqueInput
  shippingMethod: ShippingMethodWhereUniqueInput
}

input ReturnWhereInput {
  AND: [ReturnWhereInput!]
  OR: [ReturnWhereInput!]
  NOT: [ReturnWhereInput!]
  id: IDFilter
  status: ReturnStatusTypeNullableFilter
  refundAmount: IntFilter
  receivedAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotification: BooleanFilter
  claimOrder: ClaimOrderWhereInput
  swap: SwapWhereInput
  order: OrderWhereInput
  returnItems: ReturnItemManyRelationFilter
  shippingMethod: ShippingMethodWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ReturnStatusTypeNullableFilter {
  equals: ReturnStatusType
  in: [ReturnStatusType!]
  notIn: [ReturnStatusType!]
  not: ReturnStatusTypeNullableFilter
}

input ReturnOrderByInput {
  id: OrderDirection
  status: OrderDirection
  refundAmount: OrderDirection
  receivedAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotification: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ReturnUpdateInput {
  status: ReturnStatusType
  shippingData: JSON
  refundAmount: Int
  receivedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  claimOrder: ClaimOrderRelateToOneForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  returnItems: ReturnItemRelateToManyForUpdateInput
  shippingMethod: ShippingMethodRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodRelateToOneForUpdateInput {
  create: ShippingMethodCreateInput
  connect: ShippingMethodWhereUniqueInput
  disconnect: Boolean
}

input ReturnUpdateArgs {
  where: ReturnWhereUniqueInput!
  data: ReturnUpdateInput!
}

input ReturnCreateInput {
  status: ReturnStatusType
  shippingData: JSON
  refundAmount: Int
  receivedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  claimOrder: ClaimOrderRelateToOneForCreateInput
  swap: SwapRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  returnItems: ReturnItemRelateToManyForCreateInput
  shippingMethod: ShippingMethodRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodRelateToOneForCreateInput {
  create: ShippingMethodCreateInput
  connect: ShippingMethodWhereUniqueInput
}

type ReturnItem {
  id: ID!
  quantity: Int
  isRequested: Boolean
  requestedQuantity: Int
  receivedQuantity: Int
  metadata: JSON
  note: String
  return: Return
  lineItem: LineItem
  returnReason: ReturnReason
  createdAt: DateTime
  updatedAt: DateTime
}

input ReturnItemWhereUniqueInput {
  id: ID
}

input ReturnItemWhereInput {
  AND: [ReturnItemWhereInput!]
  OR: [ReturnItemWhereInput!]
  NOT: [ReturnItemWhereInput!]
  id: IDFilter
  quantity: IntFilter
  isRequested: BooleanFilter
  requestedQuantity: IntNullableFilter
  receivedQuantity: IntNullableFilter
  note: StringFilter
  return: ReturnWhereInput
  lineItem: LineItemWhereInput
  returnReason: ReturnReasonWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ReturnItemOrderByInput {
  id: OrderDirection
  quantity: OrderDirection
  isRequested: OrderDirection
  requestedQuantity: OrderDirection
  receivedQuantity: OrderDirection
  note: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ReturnItemUpdateInput {
  quantity: Int
  isRequested: Boolean
  requestedQuantity: Int
  receivedQuantity: Int
  metadata: JSON
  note: String
  return: ReturnRelateToOneForUpdateInput
  lineItem: LineItemRelateToOneForUpdateInput
  returnReason: ReturnReasonRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ReturnReasonRelateToOneForUpdateInput {
  create: ReturnReasonCreateInput
  connect: ReturnReasonWhereUniqueInput
  disconnect: Boolean
}

input ReturnItemUpdateArgs {
  where: ReturnItemWhereUniqueInput!
  data: ReturnItemUpdateInput!
}

input ReturnItemCreateInput {
  quantity: Int
  isRequested: Boolean
  requestedQuantity: Int
  receivedQuantity: Int
  metadata: JSON
  note: String
  return: ReturnRelateToOneForCreateInput
  lineItem: LineItemRelateToOneForCreateInput
  returnReason: ReturnReasonRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ReturnReasonRelateToOneForCreateInput {
  create: ReturnReasonCreateInput
  connect: ReturnReasonWhereUniqueInput
}

type ReturnReason {
  id: ID!
  value: String
  label: String
  description: String
  metadata: JSON
  parentReturnReason: ReturnReason
  returnItems(where: ReturnItemWhereInput! = {}, orderBy: [ReturnItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnItemWhereUniqueInput): [ReturnItem!]
  returnItemsCount(where: ReturnItemWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ReturnReasonWhereUniqueInput {
  id: ID
  value: String
}

input ReturnReasonWhereInput {
  AND: [ReturnReasonWhereInput!]
  OR: [ReturnReasonWhereInput!]
  NOT: [ReturnReasonWhereInput!]
  id: IDFilter
  value: StringFilter
  label: StringFilter
  description: StringFilter
  parentReturnReason: ReturnReasonWhereInput
  returnItems: ReturnItemManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ReturnReasonOrderByInput {
  id: OrderDirection
  value: OrderDirection
  label: OrderDirection
  description: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ReturnReasonUpdateInput {
  value: String
  label: String
  description: String
  metadata: JSON
  parentReturnReason: ReturnReasonRelateToOneForUpdateInput
  returnItems: ReturnItemRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ReturnReasonUpdateArgs {
  where: ReturnReasonWhereUniqueInput!
  data: ReturnReasonUpdateInput!
}

input ReturnReasonCreateInput {
  value: String
  label: String
  description: String
  metadata: JSON
  parentReturnReason: ReturnReasonRelateToOneForCreateInput
  returnItems: ReturnItemRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Role {
  id: ID!
  name: String
  canAccessDashboard: Boolean
  canReadOrders: Boolean
  canManageOrders: Boolean
  canReadProducts: Boolean
  canManageProducts: Boolean
  canReadFulfillments: Boolean
  canManageFulfillments: Boolean
  canReadUsers: Boolean
  canManageUsers: Boolean
  canReadRoles: Boolean
  canManageRoles: Boolean
  canReadCheckouts: Boolean
  canManageCheckouts: Boolean
  canReadDiscounts: Boolean
  canManageDiscounts: Boolean
  canReadGiftCards: Boolean
  canManageGiftCards: Boolean
  canReadReturns: Boolean
  canManageReturns: Boolean
  canReadSalesChannels: Boolean
  canManageSalesChannels: Boolean
  canReadPayments: Boolean
  canManagePayments: Boolean
  canReadIdempotencyKeys: Boolean
  canManageIdempotencyKeys: Boolean
  canReadApps: Boolean
  canManageApps: Boolean
  canManageKeys: Boolean
  canManageOnboarding: Boolean
  assignedTo(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  assignedToCount(where: UserWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input RoleWhereUniqueInput {
  id: ID
}

input RoleWhereInput {
  AND: [RoleWhereInput!]
  OR: [RoleWhereInput!]
  NOT: [RoleWhereInput!]
  id: IDFilter
  name: StringFilter
  canAccessDashboard: BooleanFilter
  canReadOrders: BooleanFilter
  canManageOrders: BooleanFilter
  canReadProducts: BooleanFilter
  canManageProducts: BooleanFilter
  canReadFulfillments: BooleanFilter
  canManageFulfillments: BooleanFilter
  canReadUsers: BooleanFilter
  canManageUsers: BooleanFilter
  canReadRoles: BooleanFilter
  canManageRoles: BooleanFilter
  canReadCheckouts: BooleanFilter
  canManageCheckouts: BooleanFilter
  canReadDiscounts: BooleanFilter
  canManageDiscounts: BooleanFilter
  canReadGiftCards: BooleanFilter
  canManageGiftCards: BooleanFilter
  canReadReturns: BooleanFilter
  canManageReturns: BooleanFilter
  canReadSalesChannels: BooleanFilter
  canManageSalesChannels: BooleanFilter
  canReadPayments: BooleanFilter
  canManagePayments: BooleanFilter
  canReadIdempotencyKeys: BooleanFilter
  canManageIdempotencyKeys: BooleanFilter
  canReadApps: BooleanFilter
  canManageApps: BooleanFilter
  canManageKeys: BooleanFilter
  canManageOnboarding: BooleanFilter
  assignedTo: UserManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input RoleOrderByInput {
  id: OrderDirection
  name: OrderDirection
  canAccessDashboard: OrderDirection
  canReadOrders: OrderDirection
  canManageOrders: OrderDirection
  canReadProducts: OrderDirection
  canManageProducts: OrderDirection
  canReadFulfillments: OrderDirection
  canManageFulfillments: OrderDirection
  canReadUsers: OrderDirection
  canManageUsers: OrderDirection
  canReadRoles: OrderDirection
  canManageRoles: OrderDirection
  canReadCheckouts: OrderDirection
  canManageCheckouts: OrderDirection
  canReadDiscounts: OrderDirection
  canManageDiscounts: OrderDirection
  canReadGiftCards: OrderDirection
  canManageGiftCards: OrderDirection
  canReadReturns: OrderDirection
  canManageReturns: OrderDirection
  canReadSalesChannels: OrderDirection
  canManageSalesChannels: OrderDirection
  canReadPayments: OrderDirection
  canManagePayments: OrderDirection
  canReadIdempotencyKeys: OrderDirection
  canManageIdempotencyKeys: OrderDirection
  canReadApps: OrderDirection
  canManageApps: OrderDirection
  canManageKeys: OrderDirection
  canManageOnboarding: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input RoleUpdateInput {
  name: String
  canAccessDashboard: Boolean
  canReadOrders: Boolean
  canManageOrders: Boolean
  canReadProducts: Boolean
  canManageProducts: Boolean
  canReadFulfillments: Boolean
  canManageFulfillments: Boolean
  canReadUsers: Boolean
  canManageUsers: Boolean
  canReadRoles: Boolean
  canManageRoles: Boolean
  canReadCheckouts: Boolean
  canManageCheckouts: Boolean
  canReadDiscounts: Boolean
  canManageDiscounts: Boolean
  canReadGiftCards: Boolean
  canManageGiftCards: Boolean
  canReadReturns: Boolean
  canManageReturns: Boolean
  canReadSalesChannels: Boolean
  canManageSalesChannels: Boolean
  canReadPayments: Boolean
  canManagePayments: Boolean
  canReadIdempotencyKeys: Boolean
  canManageIdempotencyKeys: Boolean
  canReadApps: Boolean
  canManageApps: Boolean
  canManageKeys: Boolean
  canManageOnboarding: Boolean
  assignedTo: UserRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input RoleUpdateArgs {
  where: RoleWhereUniqueInput!
  data: RoleUpdateInput!
}

input RoleCreateInput {
  name: String
  canAccessDashboard: Boolean
  canReadOrders: Boolean
  canManageOrders: Boolean
  canReadProducts: Boolean
  canManageProducts: Boolean
  canReadFulfillments: Boolean
  canManageFulfillments: Boolean
  canReadUsers: Boolean
  canManageUsers: Boolean
  canReadRoles: Boolean
  canManageRoles: Boolean
  canReadCheckouts: Boolean
  canManageCheckouts: Boolean
  canReadDiscounts: Boolean
  canManageDiscounts: Boolean
  canReadGiftCards: Boolean
  canManageGiftCards: Boolean
  canReadReturns: Boolean
  canManageReturns: Boolean
  canReadSalesChannels: Boolean
  canManageSalesChannels: Boolean
  canReadPayments: Boolean
  canManagePayments: Boolean
  canReadIdempotencyKeys: Boolean
  canManageIdempotencyKeys: Boolean
  canReadApps: Boolean
  canManageApps: Boolean
  canManageKeys: Boolean
  canManageOnboarding: Boolean
  assignedTo: UserRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type RuleType {
  id: ID!
  name: String
  ruleAttribute: String
  priceSets(where: PriceSetWhereInput! = {}, orderBy: [PriceSetOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceSetWhereUniqueInput): [PriceSet!]
  priceSetsCount(where: PriceSetWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input RuleTypeWhereUniqueInput {
  id: ID
  ruleAttribute: String
}

input RuleTypeWhereInput {
  AND: [RuleTypeWhereInput!]
  OR: [RuleTypeWhereInput!]
  NOT: [RuleTypeWhereInput!]
  id: IDFilter
  name: StringFilter
  ruleAttribute: StringFilter
  priceSets: PriceSetManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input PriceSetManyRelationFilter {
  every: PriceSetWhereInput
  some: PriceSetWhereInput
  none: PriceSetWhereInput
}

input RuleTypeOrderByInput {
  id: OrderDirection
  name: OrderDirection
  ruleAttribute: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input RuleTypeUpdateInput {
  name: String
  ruleAttribute: String
  priceSets: PriceSetRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PriceSetRelateToManyForUpdateInput {
  disconnect: [PriceSetWhereUniqueInput!]
  set: [PriceSetWhereUniqueInput!]
  create: [PriceSetCreateInput!]
  connect: [PriceSetWhereUniqueInput!]
}

input RuleTypeUpdateArgs {
  where: RuleTypeWhereUniqueInput!
  data: RuleTypeUpdateInput!
}

input RuleTypeCreateInput {
  name: String
  ruleAttribute: String
  priceSets: PriceSetRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input PriceSetRelateToManyForCreateInput {
  create: [PriceSetCreateInput!]
  connect: [PriceSetWhereUniqueInput!]
}

type SalesChannel {
  id: ID!
  name: String
  description: String
  isDisabled: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

input SalesChannelWhereUniqueInput {
  id: ID
}

input SalesChannelWhereInput {
  AND: [SalesChannelWhereInput!]
  OR: [SalesChannelWhereInput!]
  NOT: [SalesChannelWhereInput!]
  id: IDFilter
  name: StringFilter
  description: StringFilter
  isDisabled: BooleanFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input SalesChannelOrderByInput {
  id: OrderDirection
  name: OrderDirection
  description: OrderDirection
  isDisabled: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input SalesChannelUpdateInput {
  name: String
  description: String
  isDisabled: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

input SalesChannelUpdateArgs {
  where: SalesChannelWhereUniqueInput!
  data: SalesChannelUpdateInput!
}

input SalesChannelCreateInput {
  name: String
  description: String
  isDisabled: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

type ShippingLabel {
  id: ID!
  status: ShippingLabelStatusType
  labelUrl: String
  carrier: String
  service: String
  rate: JSON
  trackingNumber: String
  trackingUrl: String
  order: Order
  provider: ShippingProvider
  fulfillment: Fulfillment
  data: JSON
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

enum ShippingLabelStatusType {
  created
  purchased
  failed
}

input ShippingLabelWhereUniqueInput {
  id: ID
}

input ShippingLabelWhereInput {
  AND: [ShippingLabelWhereInput!]
  OR: [ShippingLabelWhereInput!]
  NOT: [ShippingLabelWhereInput!]
  id: IDFilter
  status: ShippingLabelStatusTypeNullableFilter
  labelUrl: StringFilter
  carrier: StringFilter
  service: StringFilter
  trackingNumber: StringFilter
  trackingUrl: StringFilter
  order: OrderWhereInput
  provider: ShippingProviderWhereInput
  fulfillment: FulfillmentWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingLabelStatusTypeNullableFilter {
  equals: ShippingLabelStatusType
  in: [ShippingLabelStatusType!]
  notIn: [ShippingLabelStatusType!]
  not: ShippingLabelStatusTypeNullableFilter
}

input ShippingLabelOrderByInput {
  id: OrderDirection
  status: OrderDirection
  labelUrl: OrderDirection
  carrier: OrderDirection
  service: OrderDirection
  trackingNumber: OrderDirection
  trackingUrl: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingLabelUpdateInput {
  status: ShippingLabelStatusType
  labelUrl: String
  carrier: String
  service: String
  rate: JSON
  trackingNumber: String
  trackingUrl: String
  order: OrderRelateToOneForUpdateInput
  provider: ShippingProviderRelateToOneForUpdateInput
  fulfillment: FulfillmentRelateToOneForUpdateInput
  data: JSON
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProviderRelateToOneForUpdateInput {
  create: ShippingProviderCreateInput
  connect: ShippingProviderWhereUniqueInput
  disconnect: Boolean
}

input ShippingLabelUpdateArgs {
  where: ShippingLabelWhereUniqueInput!
  data: ShippingLabelUpdateInput!
}

input ShippingLabelCreateInput {
  status: ShippingLabelStatusType
  labelUrl: String
  carrier: String
  service: String
  rate: JSON
  trackingNumber: String
  trackingUrl: String
  order: OrderRelateToOneForCreateInput
  provider: ShippingProviderRelateToOneForCreateInput
  fulfillment: FulfillmentRelateToOneForCreateInput
  data: JSON
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProviderRelateToOneForCreateInput {
  create: ShippingProviderCreateInput
  connect: ShippingProviderWhereUniqueInput
}

type ShippingMethod {
  id: ID!
  price: Int
  data: JSON
  return: Return
  order: Order
  claimOrder: ClaimOrder
  cart: Cart
  swap: Swap
  shippingOption: ShippingOption
  shippingMethodTaxLines(where: ShippingMethodTaxLineWhereInput! = {}, orderBy: [ShippingMethodTaxLineOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodTaxLineWhereUniqueInput): [ShippingMethodTaxLine!]
  shippingMethodTaxLinesCount(where: ShippingMethodTaxLineWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodWhereUniqueInput {
  id: ID
  return: ReturnWhereUniqueInput
}

input ShippingMethodWhereInput {
  AND: [ShippingMethodWhereInput!]
  OR: [ShippingMethodWhereInput!]
  NOT: [ShippingMethodWhereInput!]
  id: IDFilter
  price: IntFilter
  return: ReturnWhereInput
  order: OrderWhereInput
  claimOrder: ClaimOrderWhereInput
  cart: CartWhereInput
  swap: SwapWhereInput
  shippingOption: ShippingOptionWhereInput
  shippingMethodTaxLines: ShippingMethodTaxLineManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingMethodTaxLineManyRelationFilter {
  every: ShippingMethodTaxLineWhereInput
  some: ShippingMethodTaxLineWhereInput
  none: ShippingMethodTaxLineWhereInput
}

input ShippingMethodOrderByInput {
  id: OrderDirection
  price: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingMethodUpdateInput {
  price: Int
  data: JSON
  return: ReturnRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  claimOrder: ClaimOrderRelateToOneForUpdateInput
  cart: CartRelateToOneForUpdateInput
  swap: SwapRelateToOneForUpdateInput
  shippingOption: ShippingOptionRelateToOneForUpdateInput
  shippingMethodTaxLines: ShippingMethodTaxLineRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodTaxLineRelateToManyForUpdateInput {
  disconnect: [ShippingMethodTaxLineWhereUniqueInput!]
  set: [ShippingMethodTaxLineWhereUniqueInput!]
  create: [ShippingMethodTaxLineCreateInput!]
  connect: [ShippingMethodTaxLineWhereUniqueInput!]
}

input ShippingMethodUpdateArgs {
  where: ShippingMethodWhereUniqueInput!
  data: ShippingMethodUpdateInput!
}

input ShippingMethodCreateInput {
  price: Int
  data: JSON
  return: ReturnRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  claimOrder: ClaimOrderRelateToOneForCreateInput
  cart: CartRelateToOneForCreateInput
  swap: SwapRelateToOneForCreateInput
  shippingOption: ShippingOptionRelateToOneForCreateInput
  shippingMethodTaxLines: ShippingMethodTaxLineRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodTaxLineRelateToManyForCreateInput {
  create: [ShippingMethodTaxLineCreateInput!]
  connect: [ShippingMethodTaxLineWhereUniqueInput!]
}

type ShippingMethodTaxLine {
  id: ID!
  rate: Float
  name: String
  code: String
  metadata: JSON
  shippingMethod: ShippingMethod
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodTaxLineWhereUniqueInput {
  id: ID
}

input ShippingMethodTaxLineWhereInput {
  AND: [ShippingMethodTaxLineWhereInput!]
  OR: [ShippingMethodTaxLineWhereInput!]
  NOT: [ShippingMethodTaxLineWhereInput!]
  id: IDFilter
  rate: FloatFilter
  name: StringFilter
  code: StringFilter
  shippingMethod: ShippingMethodWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingMethodTaxLineOrderByInput {
  id: OrderDirection
  rate: OrderDirection
  name: OrderDirection
  code: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingMethodTaxLineUpdateInput {
  rate: Float
  name: String
  code: String
  metadata: JSON
  shippingMethod: ShippingMethodRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingMethodTaxLineUpdateArgs {
  where: ShippingMethodTaxLineWhereUniqueInput!
  data: ShippingMethodTaxLineUpdateInput!
}

input ShippingMethodTaxLineCreateInput {
  rate: Float
  name: String
  code: String
  metadata: JSON
  shippingMethod: ShippingMethodRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ShippingOption {
  id: ID!
  name: String
  uniqueKey: String
  priceType: ShippingOptionPriceTypeType
  amount: Int
  isReturn: Boolean
  data: JSON
  metadata: JSON
  adminOnly: Boolean
  region: Region
  fulfillmentProvider: FulfillmentProvider
  shippingProfile: ShippingProfile
  customShippingOptions(where: CustomShippingOptionWhereInput! = {}, orderBy: [CustomShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomShippingOptionWhereUniqueInput): [CustomShippingOption!]
  customShippingOptionsCount(where: CustomShippingOptionWhereInput! = {}): Int
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  shippingOptionRequirements(where: ShippingOptionRequirementWhereInput! = {}, orderBy: [ShippingOptionRequirementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionRequirementWhereUniqueInput): [ShippingOptionRequirement!]
  shippingOptionRequirementsCount(where: ShippingOptionRequirementWhereInput! = {}): Int
  taxRates(where: TaxRateWhereInput! = {}, orderBy: [TaxRateOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxRateWhereUniqueInput): [TaxRate!]
  taxRatesCount(where: TaxRateWhereInput! = {}): Int
  calculatedAmount: String
  isTaxInclusive: Boolean
  createdAt: DateTime
  updatedAt: DateTime
}

enum ShippingOptionPriceTypeType {
  flat_rate
  calculated
  free
}

input ShippingOptionWhereUniqueInput {
  id: ID
  uniqueKey: String
}

input ShippingOptionWhereInput {
  AND: [ShippingOptionWhereInput!]
  OR: [ShippingOptionWhereInput!]
  NOT: [ShippingOptionWhereInput!]
  id: IDFilter
  name: StringFilter
  uniqueKey: StringFilter
  priceType: ShippingOptionPriceTypeTypeNullableFilter
  amount: IntNullableFilter
  isReturn: BooleanFilter
  adminOnly: BooleanFilter
  region: RegionWhereInput
  fulfillmentProvider: FulfillmentProviderWhereInput
  shippingProfile: ShippingProfileWhereInput
  customShippingOptions: CustomShippingOptionManyRelationFilter
  shippingMethods: ShippingMethodManyRelationFilter
  shippingOptionRequirements: ShippingOptionRequirementManyRelationFilter
  taxRates: TaxRateManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingOptionPriceTypeTypeNullableFilter {
  equals: ShippingOptionPriceTypeType
  in: [ShippingOptionPriceTypeType!]
  notIn: [ShippingOptionPriceTypeType!]
  not: ShippingOptionPriceTypeTypeNullableFilter
}

input ShippingOptionRequirementManyRelationFilter {
  every: ShippingOptionRequirementWhereInput
  some: ShippingOptionRequirementWhereInput
  none: ShippingOptionRequirementWhereInput
}

input ShippingOptionOrderByInput {
  id: OrderDirection
  name: OrderDirection
  uniqueKey: OrderDirection
  priceType: OrderDirection
  amount: OrderDirection
  isReturn: OrderDirection
  adminOnly: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingOptionUpdateInput {
  name: String
  uniqueKey: String
  priceType: ShippingOptionPriceTypeType
  amount: Int
  isReturn: Boolean
  data: JSON
  metadata: JSON
  adminOnly: Boolean
  region: RegionRelateToOneForUpdateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForUpdateInput
  shippingProfile: ShippingProfileRelateToOneForUpdateInput
  customShippingOptions: CustomShippingOptionRelateToManyForUpdateInput
  shippingMethods: ShippingMethodRelateToManyForUpdateInput
  shippingOptionRequirements: ShippingOptionRequirementRelateToManyForUpdateInput
  taxRates: TaxRateRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRequirementRelateToManyForUpdateInput {
  disconnect: [ShippingOptionRequirementWhereUniqueInput!]
  set: [ShippingOptionRequirementWhereUniqueInput!]
  create: [ShippingOptionRequirementCreateInput!]
  connect: [ShippingOptionRequirementWhereUniqueInput!]
}

input ShippingOptionUpdateArgs {
  where: ShippingOptionWhereUniqueInput!
  data: ShippingOptionUpdateInput!
}

input ShippingOptionCreateInput {
  name: String
  uniqueKey: String
  priceType: ShippingOptionPriceTypeType
  amount: Int
  isReturn: Boolean
  data: JSON
  metadata: JSON
  adminOnly: Boolean
  region: RegionRelateToOneForCreateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForCreateInput
  shippingProfile: ShippingProfileRelateToOneForCreateInput
  customShippingOptions: CustomShippingOptionRelateToManyForCreateInput
  shippingMethods: ShippingMethodRelateToManyForCreateInput
  shippingOptionRequirements: ShippingOptionRequirementRelateToManyForCreateInput
  taxRates: TaxRateRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRequirementRelateToManyForCreateInput {
  create: [ShippingOptionRequirementCreateInput!]
  connect: [ShippingOptionRequirementWhereUniqueInput!]
}

type ShippingOptionRequirement {
  id: ID!
  type: ShippingOptionRequirementTypeType
  amount: Int
  shippingOption: ShippingOption
  createdAt: DateTime
  updatedAt: DateTime
}

enum ShippingOptionRequirementTypeType {
  min_subtotal
  max_subtotal
}

input ShippingOptionRequirementWhereUniqueInput {
  id: ID
}

input ShippingOptionRequirementWhereInput {
  AND: [ShippingOptionRequirementWhereInput!]
  OR: [ShippingOptionRequirementWhereInput!]
  NOT: [ShippingOptionRequirementWhereInput!]
  id: IDFilter
  type: ShippingOptionRequirementTypeTypeNullableFilter
  amount: IntFilter
  shippingOption: ShippingOptionWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingOptionRequirementTypeTypeNullableFilter {
  equals: ShippingOptionRequirementTypeType
  in: [ShippingOptionRequirementTypeType!]
  notIn: [ShippingOptionRequirementTypeType!]
  not: ShippingOptionRequirementTypeTypeNullableFilter
}

input ShippingOptionRequirementOrderByInput {
  id: OrderDirection
  type: OrderDirection
  amount: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingOptionRequirementUpdateInput {
  type: ShippingOptionRequirementTypeType
  amount: Int
  shippingOption: ShippingOptionRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingOptionRequirementUpdateArgs {
  where: ShippingOptionRequirementWhereUniqueInput!
  data: ShippingOptionRequirementUpdateInput!
}

input ShippingOptionRequirementCreateInput {
  type: ShippingOptionRequirementTypeType
  amount: Int
  shippingOption: ShippingOptionRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ShippingProfile {
  id: ID!
  name: String
  type: ShippingProfileTypeType
  metadata: JSON
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  shippingOptions(where: ShippingOptionWhereInput! = {}, orderBy: [ShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionWhereUniqueInput): [ShippingOption!]
  shippingOptionsCount(where: ShippingOptionWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum ShippingProfileTypeType {
  default
  gift_card
  custom
}

input ShippingProfileWhereUniqueInput {
  id: ID
}

input ShippingProfileWhereInput {
  AND: [ShippingProfileWhereInput!]
  OR: [ShippingProfileWhereInput!]
  NOT: [ShippingProfileWhereInput!]
  id: IDFilter
  name: StringFilter
  type: ShippingProfileTypeTypeNullableFilter
  products: ProductManyRelationFilter
  shippingOptions: ShippingOptionManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingProfileTypeTypeNullableFilter {
  equals: ShippingProfileTypeType
  in: [ShippingProfileTypeType!]
  notIn: [ShippingProfileTypeType!]
  not: ShippingProfileTypeTypeNullableFilter
}

input ShippingProfileOrderByInput {
  id: OrderDirection
  name: OrderDirection
  type: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingProfileUpdateInput {
  name: String
  type: ShippingProfileTypeType
  metadata: JSON
  products: ProductRelateToManyForUpdateInput
  shippingOptions: ShippingOptionRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProfileUpdateArgs {
  where: ShippingProfileWhereUniqueInput!
  data: ShippingProfileUpdateInput!
}

input ShippingProfileCreateInput {
  name: String
  type: ShippingProfileTypeType
  metadata: JSON
  products: ProductRelateToManyForCreateInput
  shippingOptions: ShippingOptionRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type ShippingProvider {
  id: ID!
  name: String
  isActive: Boolean
  accessToken: String
  createLabelFunction: String
  getRatesFunction: String
  validateAddressFunction: String
  trackShipmentFunction: String
  cancelLabelFunction: String
  metadata: JSON
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  labels(where: ShippingLabelWhereInput! = {}, orderBy: [ShippingLabelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingLabelWhereUniqueInput): [ShippingLabel!]
  labelsCount(where: ShippingLabelWhereInput! = {}): Int
  fulfillmentProvider: FulfillmentProvider
  fromAddress: Address
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProviderWhereUniqueInput {
  id: ID
}

input ShippingProviderWhereInput {
  AND: [ShippingProviderWhereInput!]
  OR: [ShippingProviderWhereInput!]
  NOT: [ShippingProviderWhereInput!]
  id: IDFilter
  name: StringFilter
  isActive: BooleanFilter
  accessToken: StringFilter
  createLabelFunction: StringFilter
  getRatesFunction: StringFilter
  validateAddressFunction: StringFilter
  trackShipmentFunction: StringFilter
  cancelLabelFunction: StringFilter
  regions: RegionManyRelationFilter
  labels: ShippingLabelManyRelationFilter
  fulfillmentProvider: FulfillmentProviderWhereInput
  fromAddress: AddressWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input ShippingProviderOrderByInput {
  id: OrderDirection
  name: OrderDirection
  isActive: OrderDirection
  accessToken: OrderDirection
  createLabelFunction: OrderDirection
  getRatesFunction: OrderDirection
  validateAddressFunction: OrderDirection
  trackShipmentFunction: OrderDirection
  cancelLabelFunction: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input ShippingProviderUpdateInput {
  name: String
  isActive: Boolean
  accessToken: String
  createLabelFunction: String
  getRatesFunction: String
  validateAddressFunction: String
  trackShipmentFunction: String
  cancelLabelFunction: String
  metadata: JSON
  regions: RegionRelateToManyForUpdateInput
  labels: ShippingLabelRelateToManyForUpdateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForUpdateInput
  fromAddress: AddressRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input ShippingProviderUpdateArgs {
  where: ShippingProviderWhereUniqueInput!
  data: ShippingProviderUpdateInput!
}

input ShippingProviderCreateInput {
  name: String
  isActive: Boolean
  accessToken: String
  createLabelFunction: String
  getRatesFunction: String
  validateAddressFunction: String
  trackShipmentFunction: String
  cancelLabelFunction: String
  metadata: JSON
  regions: RegionRelateToManyForCreateInput
  labels: ShippingLabelRelateToManyForCreateInput
  fulfillmentProvider: FulfillmentProviderRelateToOneForCreateInput
  fromAddress: AddressRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type StockMovement {
  id: ID!
  type: StockMovementTypeType
  quantity: Int
  reason: String
  note: String
  variant: ProductVariant
  createdAt: DateTime
  updatedAt: DateTime
}

enum StockMovementTypeType {
  RECEIVE
  REMOVE
}

input StockMovementWhereUniqueInput {
  id: ID
}

input StockMovementWhereInput {
  AND: [StockMovementWhereInput!]
  OR: [StockMovementWhereInput!]
  NOT: [StockMovementWhereInput!]
  id: IDFilter
  type: StockMovementTypeTypeNullableFilter
  quantity: IntFilter
  reason: StringFilter
  note: StringFilter
  variant: ProductVariantWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input StockMovementTypeTypeNullableFilter {
  equals: StockMovementTypeType
  in: [StockMovementTypeType!]
  notIn: [StockMovementTypeType!]
  not: StockMovementTypeTypeNullableFilter
}

input StockMovementOrderByInput {
  id: OrderDirection
  type: OrderDirection
  quantity: OrderDirection
  reason: OrderDirection
  note: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input StockMovementUpdateInput {
  type: StockMovementTypeType
  quantity: Int
  reason: String
  note: String
  variant: ProductVariantRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input StockMovementUpdateArgs {
  where: StockMovementWhereUniqueInput!
  data: StockMovementUpdateInput!
}

input StockMovementCreateInput {
  type: StockMovementTypeType
  quantity: Int
  reason: String
  note: String
  variant: ProductVariantRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Store {
  id: ID!
  name: String
  defaultCurrencyCode: String
  metadata: JSON
  swapLinkTemplate: String
  paymentLinkTemplate: String
  inviteLinkTemplate: String
  currencies(where: CurrencyWhereInput! = {}, orderBy: [CurrencyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CurrencyWhereUniqueInput): [Currency!]
  currenciesCount(where: CurrencyWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input StoreWhereUniqueInput {
  id: ID
}

input StoreWhereInput {
  AND: [StoreWhereInput!]
  OR: [StoreWhereInput!]
  NOT: [StoreWhereInput!]
  id: IDFilter
  name: StringFilter
  defaultCurrencyCode: StringFilter
  swapLinkTemplate: StringFilter
  paymentLinkTemplate: StringFilter
  inviteLinkTemplate: StringFilter
  currencies: CurrencyManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input CurrencyManyRelationFilter {
  every: CurrencyWhereInput
  some: CurrencyWhereInput
  none: CurrencyWhereInput
}

input StoreOrderByInput {
  id: OrderDirection
  name: OrderDirection
  defaultCurrencyCode: OrderDirection
  swapLinkTemplate: OrderDirection
  paymentLinkTemplate: OrderDirection
  inviteLinkTemplate: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input StoreUpdateInput {
  name: String
  defaultCurrencyCode: String
  metadata: JSON
  swapLinkTemplate: String
  paymentLinkTemplate: String
  inviteLinkTemplate: String
  currencies: CurrencyRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CurrencyRelateToManyForUpdateInput {
  disconnect: [CurrencyWhereUniqueInput!]
  set: [CurrencyWhereUniqueInput!]
  create: [CurrencyCreateInput!]
  connect: [CurrencyWhereUniqueInput!]
}

input StoreUpdateArgs {
  where: StoreWhereUniqueInput!
  data: StoreUpdateInput!
}

input StoreCreateInput {
  name: String
  defaultCurrencyCode: String
  metadata: JSON
  swapLinkTemplate: String
  paymentLinkTemplate: String
  inviteLinkTemplate: String
  currencies: CurrencyRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input CurrencyRelateToManyForCreateInput {
  create: [CurrencyCreateInput!]
  connect: [CurrencyWhereUniqueInput!]
}

type Swap {
  id: ID!
  fulfillmentStatus: SwapFulfillmentStatusType
  paymentStatus: SwapPaymentStatusType
  differenceDue: Int
  confirmedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  canceledAt: DateTime
  allowBackorder: Boolean
  cart: Cart
  order: Order
  address: Address
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  fulfillments(where: FulfillmentWhereInput! = {}, orderBy: [FulfillmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentWhereUniqueInput): [Fulfillment!]
  fulfillmentsCount(where: FulfillmentWhereInput! = {}): Int
  payment: Payment
  return: Return
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

enum SwapFulfillmentStatusType {
  not_fulfilled
  fulfilled
  shipped
  partially_shipped
  canceled
  requires_action
}

enum SwapPaymentStatusType {
  not_paid
  awaiting
  captured
  confirmed
  canceled
  difference_refunded
  partially_refunded
  refunded
  requires_action
}

input SwapWhereUniqueInput {
  id: ID
  cart: CartWhereUniqueInput
  payment: PaymentWhereUniqueInput
  return: ReturnWhereUniqueInput
}

input SwapWhereInput {
  AND: [SwapWhereInput!]
  OR: [SwapWhereInput!]
  NOT: [SwapWhereInput!]
  id: IDFilter
  fulfillmentStatus: SwapFulfillmentStatusTypeNullableFilter
  paymentStatus: SwapPaymentStatusTypeNullableFilter
  differenceDue: IntNullableFilter
  confirmedAt: DateTimeNullableFilter
  idempotencyKey: StringFilter
  noNotification: BooleanFilter
  canceledAt: DateTimeNullableFilter
  allowBackorder: BooleanFilter
  cart: CartWhereInput
  order: OrderWhereInput
  address: AddressWhereInput
  lineItems: LineItemManyRelationFilter
  fulfillments: FulfillmentManyRelationFilter
  payment: PaymentWhereInput
  return: ReturnWhereInput
  shippingMethods: ShippingMethodManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input SwapFulfillmentStatusTypeNullableFilter {
  equals: SwapFulfillmentStatusType
  in: [SwapFulfillmentStatusType!]
  notIn: [SwapFulfillmentStatusType!]
  not: SwapFulfillmentStatusTypeNullableFilter
}

input SwapPaymentStatusTypeNullableFilter {
  equals: SwapPaymentStatusType
  in: [SwapPaymentStatusType!]
  notIn: [SwapPaymentStatusType!]
  not: SwapPaymentStatusTypeNullableFilter
}

input SwapOrderByInput {
  id: OrderDirection
  fulfillmentStatus: OrderDirection
  paymentStatus: OrderDirection
  differenceDue: OrderDirection
  confirmedAt: OrderDirection
  idempotencyKey: OrderDirection
  noNotification: OrderDirection
  canceledAt: OrderDirection
  allowBackorder: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input SwapUpdateInput {
  fulfillmentStatus: SwapFulfillmentStatusType
  paymentStatus: SwapPaymentStatusType
  differenceDue: Int
  confirmedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  canceledAt: DateTime
  allowBackorder: Boolean
  cart: CartRelateToOneForUpdateInput
  order: OrderRelateToOneForUpdateInput
  address: AddressRelateToOneForUpdateInput
  lineItems: LineItemRelateToManyForUpdateInput
  fulfillments: FulfillmentRelateToManyForUpdateInput
  payment: PaymentRelateToOneForUpdateInput
  return: ReturnRelateToOneForUpdateInput
  shippingMethods: ShippingMethodRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input SwapUpdateArgs {
  where: SwapWhereUniqueInput!
  data: SwapUpdateInput!
}

input SwapCreateInput {
  fulfillmentStatus: SwapFulfillmentStatusType
  paymentStatus: SwapPaymentStatusType
  differenceDue: Int
  confirmedAt: DateTime
  metadata: JSON
  idempotencyKey: String
  noNotification: Boolean
  canceledAt: DateTime
  allowBackorder: Boolean
  cart: CartRelateToOneForCreateInput
  order: OrderRelateToOneForCreateInput
  address: AddressRelateToOneForCreateInput
  lineItems: LineItemRelateToManyForCreateInput
  fulfillments: FulfillmentRelateToManyForCreateInput
  payment: PaymentRelateToOneForCreateInput
  return: ReturnRelateToOneForCreateInput
  shippingMethods: ShippingMethodRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type TaxProvider {
  id: ID!
  isInstalled: Boolean
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
}

input TaxProviderWhereUniqueInput {
  id: ID
}

input TaxProviderWhereInput {
  AND: [TaxProviderWhereInput!]
  OR: [TaxProviderWhereInput!]
  NOT: [TaxProviderWhereInput!]
  id: IDFilter
  isInstalled: BooleanFilter
  regions: RegionManyRelationFilter
}

input TaxProviderOrderByInput {
  id: OrderDirection
  isInstalled: OrderDirection
}

input TaxProviderUpdateInput {
  isInstalled: Boolean
  regions: RegionRelateToManyForUpdateInput
}

input TaxProviderUpdateArgs {
  where: TaxProviderWhereUniqueInput!
  data: TaxProviderUpdateInput!
}

input TaxProviderCreateInput {
  isInstalled: Boolean
  regions: RegionRelateToManyForCreateInput
}

type TaxRate {
  id: ID!
  rate: Float
  code: String
  name: String
  metadata: JSON
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  productTypes(where: ProductTypeWhereInput! = {}, orderBy: [ProductTypeOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTypeWhereUniqueInput): [ProductType!]
  productTypesCount(where: ProductTypeWhereInput! = {}): Int
  region: Region
  shippingOptions(where: ShippingOptionWhereInput! = {}, orderBy: [ShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionWhereUniqueInput): [ShippingOption!]
  shippingOptionsCount(where: ShippingOptionWhereInput! = {}): Int
  createdAt: DateTime
  updatedAt: DateTime
}

input TaxRateWhereUniqueInput {
  id: ID
}

input TaxRateWhereInput {
  AND: [TaxRateWhereInput!]
  OR: [TaxRateWhereInput!]
  NOT: [TaxRateWhereInput!]
  id: IDFilter
  rate: FloatNullableFilter
  code: StringFilter
  name: StringFilter
  products: ProductManyRelationFilter
  productTypes: ProductTypeManyRelationFilter
  region: RegionWhereInput
  shippingOptions: ShippingOptionManyRelationFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input TaxRateOrderByInput {
  id: OrderDirection
  rate: OrderDirection
  code: OrderDirection
  name: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input TaxRateUpdateInput {
  rate: Float
  code: String
  name: String
  metadata: JSON
  products: ProductRelateToManyForUpdateInput
  productTypes: ProductTypeRelateToManyForUpdateInput
  region: RegionRelateToOneForUpdateInput
  shippingOptions: ShippingOptionRelateToManyForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input TaxRateUpdateArgs {
  where: TaxRateWhereUniqueInput!
  data: TaxRateUpdateInput!
}

input TaxRateCreateInput {
  rate: Float
  code: String
  name: String
  metadata: JSON
  products: ProductRelateToManyForCreateInput
  productTypes: ProductTypeRelateToManyForCreateInput
  region: RegionRelateToOneForCreateInput
  shippingOptions: ShippingOptionRelateToManyForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type Team {
  id: ID!
  name: String
  description: String
  members(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  membersCount(where: UserWhereInput! = {}): Int
  leader: User
  createdAt: DateTime
  updatedAt: DateTime
}

input TeamWhereUniqueInput {
  id: ID
}

input TeamWhereInput {
  AND: [TeamWhereInput!]
  OR: [TeamWhereInput!]
  NOT: [TeamWhereInput!]
  id: IDFilter
  name: StringFilter
  description: StringFilter
  members: UserManyRelationFilter
  leader: UserWhereInput
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input TeamOrderByInput {
  id: OrderDirection
  name: OrderDirection
  description: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input TeamUpdateInput {
  name: String
  description: String
  members: UserRelateToManyForUpdateInput
  leader: UserRelateToOneForUpdateInput
  createdAt: DateTime
  updatedAt: DateTime
}

input TeamUpdateArgs {
  where: TeamWhereUniqueInput!
  data: TeamUpdateInput!
}

input TeamCreateInput {
  name: String
  description: String
  members: UserRelateToManyForCreateInput
  leader: UserRelateToOneForCreateInput
  createdAt: DateTime
  updatedAt: DateTime
}

type User {
  id: ID!
  name: String
  email: String
  password: PasswordState
  role: Role
  apiKeys(where: ApiKeyWhereInput! = {}, orderBy: [ApiKeyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ApiKeyWhereUniqueInput): [ApiKey!]
  apiKeysCount(where: ApiKeyWhereInput! = {}): Int
  phone: String
  hasAccount: Boolean
  addresses(where: AddressWhereInput! = {}, orderBy: [AddressOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: AddressWhereUniqueInput): [Address!]
  addressesCount(where: AddressWhereInput! = {}): Int
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  orderEvents(where: OrderEventWhereInput! = {}, orderBy: [OrderEventOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderEventWhereUniqueInput): [OrderEvent!]
  orderEventsCount(where: OrderEventWhereInput! = {}): Int
  carts(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsCount(where: CartWhereInput! = {}): Int
  customerGroups(where: CustomerGroupWhereInput! = {}, orderBy: [CustomerGroupOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomerGroupWhereUniqueInput): [CustomerGroup!]
  customerGroupsCount(where: CustomerGroupWhereInput! = {}): Int
  notifications(where: NotificationWhereInput! = {}, orderBy: [NotificationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NotificationWhereUniqueInput): [Notification!]
  notificationsCount(where: NotificationWhereInput! = {}): Int
  payments(where: PaymentWhereInput! = {}, orderBy: [PaymentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentWhereUniqueInput): [Payment!]
  paymentsCount(where: PaymentWhereInput! = {}): Int
  batchJobs(where: BatchJobWhereInput! = {}, orderBy: [BatchJobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BatchJobWhereUniqueInput): [BatchJob!]
  batchJobsCount(where: BatchJobWhereInput! = {}): Int
  team: Team
  teamLead(where: TeamWhereInput! = {}, orderBy: [TeamOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TeamWhereUniqueInput): [Team!]
  teamLeadCount(where: TeamWhereInput! = {}): Int
  userField: UserField
  onboardingStatus: String
  firstName: String
  lastName: String
  activeCartId: String
  billingAddress: Address
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: PasswordState
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

type PasswordState {
  isSet: Boolean!
}

input UserWhereUniqueInput {
  id: ID
  email: String
  userField: UserFieldWhereUniqueInput
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  email: StringFilter
  role: RoleWhereInput
  apiKeys: ApiKeyManyRelationFilter
  phone: StringFilter
  hasAccount: BooleanFilter
  addresses: AddressManyRelationFilter
  orders: OrderManyRelationFilter
  orderEvents: OrderEventManyRelationFilter
  carts: CartManyRelationFilter
  customerGroups: CustomerGroupManyRelationFilter
  notifications: NotificationManyRelationFilter
  payments: PaymentManyRelationFilter
  batchJobs: BatchJobManyRelationFilter
  team: TeamWhereInput
  teamLead: TeamManyRelationFilter
  userField: UserFieldWhereInput
  onboardingStatus: StringNullableFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
  passwordResetToken: PasswordFilter
  passwordResetIssuedAt: DateTimeNullableFilter
  passwordResetRedeemedAt: DateTimeNullableFilter
}

input ApiKeyManyRelationFilter {
  every: ApiKeyWhereInput
  some: ApiKeyWhereInput
  none: ApiKeyWhereInput
}

input BatchJobManyRelationFilter {
  every: BatchJobWhereInput
  some: BatchJobWhereInput
  none: BatchJobWhereInput
}

input TeamManyRelationFilter {
  every: TeamWhereInput
  some: TeamWhereInput
  none: TeamWhereInput
}

input StringNullableFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  mode: QueryMode
  not: StringNullableFilter
}

input PasswordFilter {
  isSet: Boolean!
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
  email: OrderDirection
  phone: OrderDirection
  hasAccount: OrderDirection
  onboardingStatus: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
  passwordResetIssuedAt: OrderDirection
  passwordResetRedeemedAt: OrderDirection
}

input UserUpdateInput {
  name: String
  email: String
  password: String
  role: RoleRelateToOneForUpdateInput
  apiKeys: ApiKeyRelateToManyForUpdateInput
  phone: String
  hasAccount: Boolean
  addresses: AddressRelateToManyForUpdateInput
  orders: OrderRelateToManyForUpdateInput
  orderEvents: OrderEventRelateToManyForUpdateInput
  carts: CartRelateToManyForUpdateInput
  customerGroups: CustomerGroupRelateToManyForUpdateInput
  notifications: NotificationRelateToManyForUpdateInput
  payments: PaymentRelateToManyForUpdateInput
  batchJobs: BatchJobRelateToManyForUpdateInput
  team: TeamRelateToOneForUpdateInput
  teamLead: TeamRelateToManyForUpdateInput
  userField: UserFieldRelateToOneForUpdateInput
  onboardingStatus: String
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input RoleRelateToOneForUpdateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
  disconnect: Boolean
}

input ApiKeyRelateToManyForUpdateInput {
  disconnect: [ApiKeyWhereUniqueInput!]
  set: [ApiKeyWhereUniqueInput!]
  create: [ApiKeyCreateInput!]
  connect: [ApiKeyWhereUniqueInput!]
}

input BatchJobRelateToManyForUpdateInput {
  disconnect: [BatchJobWhereUniqueInput!]
  set: [BatchJobWhereUniqueInput!]
  create: [BatchJobCreateInput!]
  connect: [BatchJobWhereUniqueInput!]
}

input TeamRelateToOneForUpdateInput {
  create: TeamCreateInput
  connect: TeamWhereUniqueInput
  disconnect: Boolean
}

input TeamRelateToManyForUpdateInput {
  disconnect: [TeamWhereUniqueInput!]
  set: [TeamWhereUniqueInput!]
  create: [TeamCreateInput!]
  connect: [TeamWhereUniqueInput!]
}

input UserFieldRelateToOneForUpdateInput {
  create: UserFieldCreateInput
  connect: UserFieldWhereUniqueInput
  disconnect: Boolean
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  email: String
  password: String
  role: RoleRelateToOneForCreateInput
  apiKeys: ApiKeyRelateToManyForCreateInput
  phone: String
  hasAccount: Boolean
  addresses: AddressRelateToManyForCreateInput
  orders: OrderRelateToManyForCreateInput
  orderEvents: OrderEventRelateToManyForCreateInput
  carts: CartRelateToManyForCreateInput
  customerGroups: CustomerGroupRelateToManyForCreateInput
  notifications: NotificationRelateToManyForCreateInput
  payments: PaymentRelateToManyForCreateInput
  batchJobs: BatchJobRelateToManyForCreateInput
  team: TeamRelateToOneForCreateInput
  teamLead: TeamRelateToManyForCreateInput
  userField: UserFieldRelateToOneForCreateInput
  onboardingStatus: String
  createdAt: DateTime
  updatedAt: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input RoleRelateToOneForCreateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
}

input ApiKeyRelateToManyForCreateInput {
  create: [ApiKeyCreateInput!]
  connect: [ApiKeyWhereUniqueInput!]
}

input BatchJobRelateToManyForCreateInput {
  create: [BatchJobCreateInput!]
  connect: [BatchJobWhereUniqueInput!]
}

input TeamRelateToOneForCreateInput {
  create: TeamCreateInput
  connect: TeamWhereUniqueInput
}

input TeamRelateToManyForCreateInput {
  create: [TeamCreateInput!]
  connect: [TeamWhereUniqueInput!]
}

input UserFieldRelateToOneForCreateInput {
  create: UserFieldCreateInput
  connect: UserFieldWhereUniqueInput
}

type UserField {
  id: ID!
  user: User
  lastLoginIp: String
  lastLoginUserAgent: String
  loginHistory: JSON
  preferences: JSON
  notes: String
  lastPasswordChange: DateTime
  failedLoginAttempts: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input UserFieldWhereUniqueInput {
  id: ID
  user: UserWhereUniqueInput
}

input UserFieldWhereInput {
  AND: [UserFieldWhereInput!]
  OR: [UserFieldWhereInput!]
  NOT: [UserFieldWhereInput!]
  id: IDFilter
  user: UserWhereInput
  lastLoginIp: StringFilter
  lastLoginUserAgent: StringFilter
  notes: StringFilter
  lastPasswordChange: DateTimeNullableFilter
  createdAt: DateTimeFilter
  updatedAt: DateTimeFilter
}

input UserFieldOrderByInput {
  id: OrderDirection
  lastLoginIp: OrderDirection
  lastLoginUserAgent: OrderDirection
  notes: OrderDirection
  lastPasswordChange: OrderDirection
  createdAt: OrderDirection
  updatedAt: OrderDirection
}

input UserFieldUpdateInput {
  user: UserRelateToOneForUpdateInput
  lastLoginIp: String
  lastLoginUserAgent: String
  loginHistory: JSON
  preferences: JSON
  notes: String
  lastPasswordChange: DateTime
  failedLoginAttempts: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

input UserFieldUpdateArgs {
  where: UserFieldWhereUniqueInput!
  data: UserFieldUpdateInput!
}

input UserFieldCreateInput {
  user: UserRelateToOneForCreateInput
  lastLoginIp: String
  lastLoginUserAgent: String
  loginHistory: JSON
  preferences: JSON
  notes: String
  lastPasswordChange: DateTime
  failedLoginAttempts: JSON
  createdAt: DateTime
  updatedAt: DateTime
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createAddress(data: AddressCreateInput!): Address
  createAddresses(data: [AddressCreateInput!]!): [Address]
  updateAddress(where: AddressWhereUniqueInput!, data: AddressUpdateInput!): Address
  updateAddresses(data: [AddressUpdateArgs!]!): [Address]
  deleteAddress(where: AddressWhereUniqueInput!): Address
  deleteAddresses(where: [AddressWhereUniqueInput!]!): [Address]
  createApiKey(data: ApiKeyCreateInput!): ApiKey
  createApiKeys(data: [ApiKeyCreateInput!]!): [ApiKey]
  updateApiKey(where: ApiKeyWhereUniqueInput!, data: ApiKeyUpdateInput!): ApiKey
  updateApiKeys(data: [ApiKeyUpdateArgs!]!): [ApiKey]
  deleteApiKey(where: ApiKeyWhereUniqueInput!): ApiKey
  deleteApiKeys(where: [ApiKeyWhereUniqueInput!]!): [ApiKey]
  createBatchJob(data: BatchJobCreateInput!): BatchJob
  createBatchJobs(data: [BatchJobCreateInput!]!): [BatchJob]
  updateBatchJob(where: BatchJobWhereUniqueInput!, data: BatchJobUpdateInput!): BatchJob
  updateBatchJobs(data: [BatchJobUpdateArgs!]!): [BatchJob]
  deleteBatchJob(where: BatchJobWhereUniqueInput!): BatchJob
  deleteBatchJobs(where: [BatchJobWhereUniqueInput!]!): [BatchJob]
  createCapture(data: CaptureCreateInput!): Capture
  createCaptures(data: [CaptureCreateInput!]!): [Capture]
  updateCapture(where: CaptureWhereUniqueInput!, data: CaptureUpdateInput!): Capture
  updateCaptures(data: [CaptureUpdateArgs!]!): [Capture]
  deleteCapture(where: CaptureWhereUniqueInput!): Capture
  deleteCaptures(where: [CaptureWhereUniqueInput!]!): [Capture]
  createCart(data: CartCreateInput!): Cart
  createCarts(data: [CartCreateInput!]!): [Cart]
  updateCart(where: CartWhereUniqueInput!, data: CartUpdateInput!): Cart
  updateCarts(data: [CartUpdateArgs!]!): [Cart]
  deleteCart(where: CartWhereUniqueInput!): Cart
  deleteCarts(where: [CartWhereUniqueInput!]!): [Cart]
  createClaimImage(data: ClaimImageCreateInput!): ClaimImage
  createClaimImages(data: [ClaimImageCreateInput!]!): [ClaimImage]
  updateClaimImage(where: ClaimImageWhereUniqueInput!, data: ClaimImageUpdateInput!): ClaimImage
  updateClaimImages(data: [ClaimImageUpdateArgs!]!): [ClaimImage]
  deleteClaimImage(where: ClaimImageWhereUniqueInput!): ClaimImage
  deleteClaimImages(where: [ClaimImageWhereUniqueInput!]!): [ClaimImage]
  createClaimItem(data: ClaimItemCreateInput!): ClaimItem
  createClaimItems(data: [ClaimItemCreateInput!]!): [ClaimItem]
  updateClaimItem(where: ClaimItemWhereUniqueInput!, data: ClaimItemUpdateInput!): ClaimItem
  updateClaimItems(data: [ClaimItemUpdateArgs!]!): [ClaimItem]
  deleteClaimItem(where: ClaimItemWhereUniqueInput!): ClaimItem
  deleteClaimItems(where: [ClaimItemWhereUniqueInput!]!): [ClaimItem]
  createClaimOrder(data: ClaimOrderCreateInput!): ClaimOrder
  createClaimOrders(data: [ClaimOrderCreateInput!]!): [ClaimOrder]
  updateClaimOrder(where: ClaimOrderWhereUniqueInput!, data: ClaimOrderUpdateInput!): ClaimOrder
  updateClaimOrders(data: [ClaimOrderUpdateArgs!]!): [ClaimOrder]
  deleteClaimOrder(where: ClaimOrderWhereUniqueInput!): ClaimOrder
  deleteClaimOrders(where: [ClaimOrderWhereUniqueInput!]!): [ClaimOrder]
  createClaimTag(data: ClaimTagCreateInput!): ClaimTag
  createClaimTags(data: [ClaimTagCreateInput!]!): [ClaimTag]
  updateClaimTag(where: ClaimTagWhereUniqueInput!, data: ClaimTagUpdateInput!): ClaimTag
  updateClaimTags(data: [ClaimTagUpdateArgs!]!): [ClaimTag]
  deleteClaimTag(where: ClaimTagWhereUniqueInput!): ClaimTag
  deleteClaimTags(where: [ClaimTagWhereUniqueInput!]!): [ClaimTag]
  createCountry(data: CountryCreateInput!): Country
  createCountries(data: [CountryCreateInput!]!): [Country]
  updateCountry(where: CountryWhereUniqueInput!, data: CountryUpdateInput!): Country
  updateCountries(data: [CountryUpdateArgs!]!): [Country]
  deleteCountry(where: CountryWhereUniqueInput!): Country
  deleteCountries(where: [CountryWhereUniqueInput!]!): [Country]
  createCurrency(data: CurrencyCreateInput!): Currency
  createCurrencies(data: [CurrencyCreateInput!]!): [Currency]
  updateCurrency(where: CurrencyWhereUniqueInput!, data: CurrencyUpdateInput!): Currency
  updateCurrencies(data: [CurrencyUpdateArgs!]!): [Currency]
  deleteCurrency(where: CurrencyWhereUniqueInput!): Currency
  deleteCurrencies(where: [CurrencyWhereUniqueInput!]!): [Currency]
  createCustomShippingOption(data: CustomShippingOptionCreateInput!): CustomShippingOption
  createCustomShippingOptions(data: [CustomShippingOptionCreateInput!]!): [CustomShippingOption]
  updateCustomShippingOption(where: CustomShippingOptionWhereUniqueInput!, data: CustomShippingOptionUpdateInput!): CustomShippingOption
  updateCustomShippingOptions(data: [CustomShippingOptionUpdateArgs!]!): [CustomShippingOption]
  deleteCustomShippingOption(where: CustomShippingOptionWhereUniqueInput!): CustomShippingOption
  deleteCustomShippingOptions(where: [CustomShippingOptionWhereUniqueInput!]!): [CustomShippingOption]
  createCustomerGroup(data: CustomerGroupCreateInput!): CustomerGroup
  createCustomerGroups(data: [CustomerGroupCreateInput!]!): [CustomerGroup]
  updateCustomerGroup(where: CustomerGroupWhereUniqueInput!, data: CustomerGroupUpdateInput!): CustomerGroup
  updateCustomerGroups(data: [CustomerGroupUpdateArgs!]!): [CustomerGroup]
  deleteCustomerGroup(where: CustomerGroupWhereUniqueInput!): CustomerGroup
  deleteCustomerGroups(where: [CustomerGroupWhereUniqueInput!]!): [CustomerGroup]
  createDiscount(data: DiscountCreateInput!): Discount
  createDiscounts(data: [DiscountCreateInput!]!): [Discount]
  updateDiscount(where: DiscountWhereUniqueInput!, data: DiscountUpdateInput!): Discount
  updateDiscounts(data: [DiscountUpdateArgs!]!): [Discount]
  deleteDiscount(where: DiscountWhereUniqueInput!): Discount
  deleteDiscounts(where: [DiscountWhereUniqueInput!]!): [Discount]
  createDiscountCondition(data: DiscountConditionCreateInput!): DiscountCondition
  createDiscountConditions(data: [DiscountConditionCreateInput!]!): [DiscountCondition]
  updateDiscountCondition(where: DiscountConditionWhereUniqueInput!, data: DiscountConditionUpdateInput!): DiscountCondition
  updateDiscountConditions(data: [DiscountConditionUpdateArgs!]!): [DiscountCondition]
  deleteDiscountCondition(where: DiscountConditionWhereUniqueInput!): DiscountCondition
  deleteDiscountConditions(where: [DiscountConditionWhereUniqueInput!]!): [DiscountCondition]
  createDiscountRule(data: DiscountRuleCreateInput!): DiscountRule
  createDiscountRules(data: [DiscountRuleCreateInput!]!): [DiscountRule]
  updateDiscountRule(where: DiscountRuleWhereUniqueInput!, data: DiscountRuleUpdateInput!): DiscountRule
  updateDiscountRules(data: [DiscountRuleUpdateArgs!]!): [DiscountRule]
  deleteDiscountRule(where: DiscountRuleWhereUniqueInput!): DiscountRule
  deleteDiscountRules(where: [DiscountRuleWhereUniqueInput!]!): [DiscountRule]
  createDraftOrder(data: DraftOrderCreateInput!): DraftOrder
  createDraftOrders(data: [DraftOrderCreateInput!]!): [DraftOrder]
  updateDraftOrder(where: DraftOrderWhereUniqueInput!, data: DraftOrderUpdateInput!): DraftOrder
  updateDraftOrders(data: [DraftOrderUpdateArgs!]!): [DraftOrder]
  deleteDraftOrder(where: DraftOrderWhereUniqueInput!): DraftOrder
  deleteDraftOrders(where: [DraftOrderWhereUniqueInput!]!): [DraftOrder]
  createFulfillment(data: FulfillmentCreateInput!): Fulfillment
  createFulfillments(data: [FulfillmentCreateInput!]!): [Fulfillment]
  updateFulfillment(where: FulfillmentWhereUniqueInput!, data: FulfillmentUpdateInput!): Fulfillment
  updateFulfillments(data: [FulfillmentUpdateArgs!]!): [Fulfillment]
  deleteFulfillment(where: FulfillmentWhereUniqueInput!): Fulfillment
  deleteFulfillments(where: [FulfillmentWhereUniqueInput!]!): [Fulfillment]
  createFulfillmentItem(data: FulfillmentItemCreateInput!): FulfillmentItem
  createFulfillmentItems(data: [FulfillmentItemCreateInput!]!): [FulfillmentItem]
  updateFulfillmentItem(where: FulfillmentItemWhereUniqueInput!, data: FulfillmentItemUpdateInput!): FulfillmentItem
  updateFulfillmentItems(data: [FulfillmentItemUpdateArgs!]!): [FulfillmentItem]
  deleteFulfillmentItem(where: FulfillmentItemWhereUniqueInput!): FulfillmentItem
  deleteFulfillmentItems(where: [FulfillmentItemWhereUniqueInput!]!): [FulfillmentItem]
  createFulfillmentProvider(data: FulfillmentProviderCreateInput!): FulfillmentProvider
  createFulfillmentProviders(data: [FulfillmentProviderCreateInput!]!): [FulfillmentProvider]
  updateFulfillmentProvider(where: FulfillmentProviderWhereUniqueInput!, data: FulfillmentProviderUpdateInput!): FulfillmentProvider
  updateFulfillmentProviders(data: [FulfillmentProviderUpdateArgs!]!): [FulfillmentProvider]
  deleteFulfillmentProvider(where: FulfillmentProviderWhereUniqueInput!): FulfillmentProvider
  deleteFulfillmentProviders(where: [FulfillmentProviderWhereUniqueInput!]!): [FulfillmentProvider]
  createGiftCard(data: GiftCardCreateInput!): GiftCard
  createGiftCards(data: [GiftCardCreateInput!]!): [GiftCard]
  updateGiftCard(where: GiftCardWhereUniqueInput!, data: GiftCardUpdateInput!): GiftCard
  updateGiftCards(data: [GiftCardUpdateArgs!]!): [GiftCard]
  deleteGiftCard(where: GiftCardWhereUniqueInput!): GiftCard
  deleteGiftCards(where: [GiftCardWhereUniqueInput!]!): [GiftCard]
  createGiftCardTransaction(data: GiftCardTransactionCreateInput!): GiftCardTransaction
  createGiftCardTransactions(data: [GiftCardTransactionCreateInput!]!): [GiftCardTransaction]
  updateGiftCardTransaction(where: GiftCardTransactionWhereUniqueInput!, data: GiftCardTransactionUpdateInput!): GiftCardTransaction
  updateGiftCardTransactions(data: [GiftCardTransactionUpdateArgs!]!): [GiftCardTransaction]
  deleteGiftCardTransaction(where: GiftCardTransactionWhereUniqueInput!): GiftCardTransaction
  deleteGiftCardTransactions(where: [GiftCardTransactionWhereUniqueInput!]!): [GiftCardTransaction]
  createIdempotencyKey(data: IdempotencyKeyCreateInput!): IdempotencyKey
  createIdempotencyKeys(data: [IdempotencyKeyCreateInput!]!): [IdempotencyKey]
  updateIdempotencyKey(where: IdempotencyKeyWhereUniqueInput!, data: IdempotencyKeyUpdateInput!): IdempotencyKey
  updateIdempotencyKeys(data: [IdempotencyKeyUpdateArgs!]!): [IdempotencyKey]
  deleteIdempotencyKey(where: IdempotencyKeyWhereUniqueInput!): IdempotencyKey
  deleteIdempotencyKeys(where: [IdempotencyKeyWhereUniqueInput!]!): [IdempotencyKey]
  createInvite(data: InviteCreateInput!): Invite
  createInvites(data: [InviteCreateInput!]!): [Invite]
  updateInvite(where: InviteWhereUniqueInput!, data: InviteUpdateInput!): Invite
  updateInvites(data: [InviteUpdateArgs!]!): [Invite]
  deleteInvite(where: InviteWhereUniqueInput!): Invite
  deleteInvites(where: [InviteWhereUniqueInput!]!): [Invite]
  createLineItem(data: LineItemCreateInput!): LineItem
  createLineItems(data: [LineItemCreateInput!]!): [LineItem]
  updateLineItem(where: LineItemWhereUniqueInput!, data: LineItemUpdateInput!): LineItem
  updateLineItems(data: [LineItemUpdateArgs!]!): [LineItem]
  deleteLineItem(where: LineItemWhereUniqueInput!): LineItem
  deleteLineItems(where: [LineItemWhereUniqueInput!]!): [LineItem]
  createLineItemAdjustment(data: LineItemAdjustmentCreateInput!): LineItemAdjustment
  createLineItemAdjustments(data: [LineItemAdjustmentCreateInput!]!): [LineItemAdjustment]
  updateLineItemAdjustment(where: LineItemAdjustmentWhereUniqueInput!, data: LineItemAdjustmentUpdateInput!): LineItemAdjustment
  updateLineItemAdjustments(data: [LineItemAdjustmentUpdateArgs!]!): [LineItemAdjustment]
  deleteLineItemAdjustment(where: LineItemAdjustmentWhereUniqueInput!): LineItemAdjustment
  deleteLineItemAdjustments(where: [LineItemAdjustmentWhereUniqueInput!]!): [LineItemAdjustment]
  createLineItemTaxLine(data: LineItemTaxLineCreateInput!): LineItemTaxLine
  createLineItemTaxLines(data: [LineItemTaxLineCreateInput!]!): [LineItemTaxLine]
  updateLineItemTaxLine(where: LineItemTaxLineWhereUniqueInput!, data: LineItemTaxLineUpdateInput!): LineItemTaxLine
  updateLineItemTaxLines(data: [LineItemTaxLineUpdateArgs!]!): [LineItemTaxLine]
  deleteLineItemTaxLine(where: LineItemTaxLineWhereUniqueInput!): LineItemTaxLine
  deleteLineItemTaxLines(where: [LineItemTaxLineWhereUniqueInput!]!): [LineItemTaxLine]
  createLocation(data: LocationCreateInput!): Location
  createLocations(data: [LocationCreateInput!]!): [Location]
  updateLocation(where: LocationWhereUniqueInput!, data: LocationUpdateInput!): Location
  updateLocations(data: [LocationUpdateArgs!]!): [Location]
  deleteLocation(where: LocationWhereUniqueInput!): Location
  deleteLocations(where: [LocationWhereUniqueInput!]!): [Location]
  createMeasurement(data: MeasurementCreateInput!): Measurement
  createMeasurements(data: [MeasurementCreateInput!]!): [Measurement]
  updateMeasurement(where: MeasurementWhereUniqueInput!, data: MeasurementUpdateInput!): Measurement
  updateMeasurements(data: [MeasurementUpdateArgs!]!): [Measurement]
  deleteMeasurement(where: MeasurementWhereUniqueInput!): Measurement
  deleteMeasurements(where: [MeasurementWhereUniqueInput!]!): [Measurement]
  createMoneyAmount(data: MoneyAmountCreateInput!): MoneyAmount
  createMoneyAmounts(data: [MoneyAmountCreateInput!]!): [MoneyAmount]
  updateMoneyAmount(where: MoneyAmountWhereUniqueInput!, data: MoneyAmountUpdateInput!): MoneyAmount
  updateMoneyAmounts(data: [MoneyAmountUpdateArgs!]!): [MoneyAmount]
  deleteMoneyAmount(where: MoneyAmountWhereUniqueInput!): MoneyAmount
  deleteMoneyAmounts(where: [MoneyAmountWhereUniqueInput!]!): [MoneyAmount]
  createNote(data: NoteCreateInput!): Note
  createNotes(data: [NoteCreateInput!]!): [Note]
  updateNote(where: NoteWhereUniqueInput!, data: NoteUpdateInput!): Note
  updateNotes(data: [NoteUpdateArgs!]!): [Note]
  deleteNote(where: NoteWhereUniqueInput!): Note
  deleteNotes(where: [NoteWhereUniqueInput!]!): [Note]
  createNotification(data: NotificationCreateInput!): Notification
  createNotifications(data: [NotificationCreateInput!]!): [Notification]
  updateNotification(where: NotificationWhereUniqueInput!, data: NotificationUpdateInput!): Notification
  updateNotifications(data: [NotificationUpdateArgs!]!): [Notification]
  deleteNotification(where: NotificationWhereUniqueInput!): Notification
  deleteNotifications(where: [NotificationWhereUniqueInput!]!): [Notification]
  createNotificationProvider(data: NotificationProviderCreateInput!): NotificationProvider
  createNotificationProviders(data: [NotificationProviderCreateInput!]!): [NotificationProvider]
  updateNotificationProvider(where: NotificationProviderWhereUniqueInput!, data: NotificationProviderUpdateInput!): NotificationProvider
  updateNotificationProviders(data: [NotificationProviderUpdateArgs!]!): [NotificationProvider]
  deleteNotificationProvider(where: NotificationProviderWhereUniqueInput!): NotificationProvider
  deleteNotificationProviders(where: [NotificationProviderWhereUniqueInput!]!): [NotificationProvider]
  createOAuth(data: OAuthCreateInput!): OAuth
  createOAuths(data: [OAuthCreateInput!]!): [OAuth]
  updateOAuth(where: OAuthWhereUniqueInput!, data: OAuthUpdateInput!): OAuth
  updateOAuths(data: [OAuthUpdateArgs!]!): [OAuth]
  deleteOAuth(where: OAuthWhereUniqueInput!): OAuth
  deleteOAuths(where: [OAuthWhereUniqueInput!]!): [OAuth]
  createOrder(data: OrderCreateInput!): Order
  createOrders(data: [OrderCreateInput!]!): [Order]
  updateOrder(where: OrderWhereUniqueInput!, data: OrderUpdateInput!): Order
  updateOrders(data: [OrderUpdateArgs!]!): [Order]
  deleteOrder(where: OrderWhereUniqueInput!): Order
  deleteOrders(where: [OrderWhereUniqueInput!]!): [Order]
  createOrderEvent(data: OrderEventCreateInput!): OrderEvent
  createOrderEvents(data: [OrderEventCreateInput!]!): [OrderEvent]
  updateOrderEvent(where: OrderEventWhereUniqueInput!, data: OrderEventUpdateInput!): OrderEvent
  updateOrderEvents(data: [OrderEventUpdateArgs!]!): [OrderEvent]
  deleteOrderEvent(where: OrderEventWhereUniqueInput!): OrderEvent
  deleteOrderEvents(where: [OrderEventWhereUniqueInput!]!): [OrderEvent]
  createOrderLineItem(data: OrderLineItemCreateInput!): OrderLineItem
  createOrderLineItems(data: [OrderLineItemCreateInput!]!): [OrderLineItem]
  updateOrderLineItem(where: OrderLineItemWhereUniqueInput!, data: OrderLineItemUpdateInput!): OrderLineItem
  updateOrderLineItems(data: [OrderLineItemUpdateArgs!]!): [OrderLineItem]
  deleteOrderLineItem(where: OrderLineItemWhereUniqueInput!): OrderLineItem
  deleteOrderLineItems(where: [OrderLineItemWhereUniqueInput!]!): [OrderLineItem]
  createOrderMoneyAmount(data: OrderMoneyAmountCreateInput!): OrderMoneyAmount
  createOrderMoneyAmounts(data: [OrderMoneyAmountCreateInput!]!): [OrderMoneyAmount]
  updateOrderMoneyAmount(where: OrderMoneyAmountWhereUniqueInput!, data: OrderMoneyAmountUpdateInput!): OrderMoneyAmount
  updateOrderMoneyAmounts(data: [OrderMoneyAmountUpdateArgs!]!): [OrderMoneyAmount]
  deleteOrderMoneyAmount(where: OrderMoneyAmountWhereUniqueInput!): OrderMoneyAmount
  deleteOrderMoneyAmounts(where: [OrderMoneyAmountWhereUniqueInput!]!): [OrderMoneyAmount]
  createPayment(data: PaymentCreateInput!): Payment
  createPayments(data: [PaymentCreateInput!]!): [Payment]
  updatePayment(where: PaymentWhereUniqueInput!, data: PaymentUpdateInput!): Payment
  updatePayments(data: [PaymentUpdateArgs!]!): [Payment]
  deletePayment(where: PaymentWhereUniqueInput!): Payment
  deletePayments(where: [PaymentWhereUniqueInput!]!): [Payment]
  createPaymentCollection(data: PaymentCollectionCreateInput!): PaymentCollection
  createPaymentCollections(data: [PaymentCollectionCreateInput!]!): [PaymentCollection]
  updatePaymentCollection(where: PaymentCollectionWhereUniqueInput!, data: PaymentCollectionUpdateInput!): PaymentCollection
  updatePaymentCollections(data: [PaymentCollectionUpdateArgs!]!): [PaymentCollection]
  deletePaymentCollection(where: PaymentCollectionWhereUniqueInput!): PaymentCollection
  deletePaymentCollections(where: [PaymentCollectionWhereUniqueInput!]!): [PaymentCollection]
  createPaymentProvider(data: PaymentProviderCreateInput!): PaymentProvider
  createPaymentProviders(data: [PaymentProviderCreateInput!]!): [PaymentProvider]
  updatePaymentProvider(where: PaymentProviderWhereUniqueInput!, data: PaymentProviderUpdateInput!): PaymentProvider
  updatePaymentProviders(data: [PaymentProviderUpdateArgs!]!): [PaymentProvider]
  deletePaymentProvider(where: PaymentProviderWhereUniqueInput!): PaymentProvider
  deletePaymentProviders(where: [PaymentProviderWhereUniqueInput!]!): [PaymentProvider]
  createPaymentSession(data: PaymentSessionCreateInput!): PaymentSession
  createPaymentSessions(data: [PaymentSessionCreateInput!]!): [PaymentSession]
  updatePaymentSession(where: PaymentSessionWhereUniqueInput!, data: PaymentSessionUpdateInput!): PaymentSession
  updatePaymentSessions(data: [PaymentSessionUpdateArgs!]!): [PaymentSession]
  deletePaymentSession(where: PaymentSessionWhereUniqueInput!): PaymentSession
  deletePaymentSessions(where: [PaymentSessionWhereUniqueInput!]!): [PaymentSession]
  createPriceList(data: PriceListCreateInput!): PriceList
  createPriceLists(data: [PriceListCreateInput!]!): [PriceList]
  updatePriceList(where: PriceListWhereUniqueInput!, data: PriceListUpdateInput!): PriceList
  updatePriceLists(data: [PriceListUpdateArgs!]!): [PriceList]
  deletePriceList(where: PriceListWhereUniqueInput!): PriceList
  deletePriceLists(where: [PriceListWhereUniqueInput!]!): [PriceList]
  createPriceRule(data: PriceRuleCreateInput!): PriceRule
  createPriceRules(data: [PriceRuleCreateInput!]!): [PriceRule]
  updatePriceRule(where: PriceRuleWhereUniqueInput!, data: PriceRuleUpdateInput!): PriceRule
  updatePriceRules(data: [PriceRuleUpdateArgs!]!): [PriceRule]
  deletePriceRule(where: PriceRuleWhereUniqueInput!): PriceRule
  deletePriceRules(where: [PriceRuleWhereUniqueInput!]!): [PriceRule]
  createPriceSet(data: PriceSetCreateInput!): PriceSet
  createPriceSets(data: [PriceSetCreateInput!]!): [PriceSet]
  updatePriceSet(where: PriceSetWhereUniqueInput!, data: PriceSetUpdateInput!): PriceSet
  updatePriceSets(data: [PriceSetUpdateArgs!]!): [PriceSet]
  deletePriceSet(where: PriceSetWhereUniqueInput!): PriceSet
  deletePriceSets(where: [PriceSetWhereUniqueInput!]!): [PriceSet]
  createProduct(data: ProductCreateInput!): Product
  createProducts(data: [ProductCreateInput!]!): [Product]
  updateProduct(where: ProductWhereUniqueInput!, data: ProductUpdateInput!): Product
  updateProducts(data: [ProductUpdateArgs!]!): [Product]
  deleteProduct(where: ProductWhereUniqueInput!): Product
  deleteProducts(where: [ProductWhereUniqueInput!]!): [Product]
  createProductCategory(data: ProductCategoryCreateInput!): ProductCategory
  createProductCategories(data: [ProductCategoryCreateInput!]!): [ProductCategory]
  updateProductCategory(where: ProductCategoryWhereUniqueInput!, data: ProductCategoryUpdateInput!): ProductCategory
  updateProductCategories(data: [ProductCategoryUpdateArgs!]!): [ProductCategory]
  deleteProductCategory(where: ProductCategoryWhereUniqueInput!): ProductCategory
  deleteProductCategories(where: [ProductCategoryWhereUniqueInput!]!): [ProductCategory]
  createProductCollection(data: ProductCollectionCreateInput!): ProductCollection
  createProductCollections(data: [ProductCollectionCreateInput!]!): [ProductCollection]
  updateProductCollection(where: ProductCollectionWhereUniqueInput!, data: ProductCollectionUpdateInput!): ProductCollection
  updateProductCollections(data: [ProductCollectionUpdateArgs!]!): [ProductCollection]
  deleteProductCollection(where: ProductCollectionWhereUniqueInput!): ProductCollection
  deleteProductCollections(where: [ProductCollectionWhereUniqueInput!]!): [ProductCollection]
  createProductImage(data: ProductImageCreateInput!): ProductImage
  createProductImages(data: [ProductImageCreateInput!]!): [ProductImage]
  updateProductImage(where: ProductImageWhereUniqueInput!, data: ProductImageUpdateInput!): ProductImage
  updateProductImages(data: [ProductImageUpdateArgs!]!): [ProductImage]
  deleteProductImage(where: ProductImageWhereUniqueInput!): ProductImage
  deleteProductImages(where: [ProductImageWhereUniqueInput!]!): [ProductImage]
  createProductOption(data: ProductOptionCreateInput!): ProductOption
  createProductOptions(data: [ProductOptionCreateInput!]!): [ProductOption]
  updateProductOption(where: ProductOptionWhereUniqueInput!, data: ProductOptionUpdateInput!): ProductOption
  updateProductOptions(data: [ProductOptionUpdateArgs!]!): [ProductOption]
  deleteProductOption(where: ProductOptionWhereUniqueInput!): ProductOption
  deleteProductOptions(where: [ProductOptionWhereUniqueInput!]!): [ProductOption]
  createProductOptionValue(data: ProductOptionValueCreateInput!): ProductOptionValue
  createProductOptionValues(data: [ProductOptionValueCreateInput!]!): [ProductOptionValue]
  updateProductOptionValue(where: ProductOptionValueWhereUniqueInput!, data: ProductOptionValueUpdateInput!): ProductOptionValue
  updateProductOptionValues(data: [ProductOptionValueUpdateArgs!]!): [ProductOptionValue]
  deleteProductOptionValue(where: ProductOptionValueWhereUniqueInput!): ProductOptionValue
  deleteProductOptionValues(where: [ProductOptionValueWhereUniqueInput!]!): [ProductOptionValue]
  createProductTag(data: ProductTagCreateInput!): ProductTag
  createProductTags(data: [ProductTagCreateInput!]!): [ProductTag]
  updateProductTag(where: ProductTagWhereUniqueInput!, data: ProductTagUpdateInput!): ProductTag
  updateProductTags(data: [ProductTagUpdateArgs!]!): [ProductTag]
  deleteProductTag(where: ProductTagWhereUniqueInput!): ProductTag
  deleteProductTags(where: [ProductTagWhereUniqueInput!]!): [ProductTag]
  createProductType(data: ProductTypeCreateInput!): ProductType
  createProductTypes(data: [ProductTypeCreateInput!]!): [ProductType]
  updateProductType(where: ProductTypeWhereUniqueInput!, data: ProductTypeUpdateInput!): ProductType
  updateProductTypes(data: [ProductTypeUpdateArgs!]!): [ProductType]
  deleteProductType(where: ProductTypeWhereUniqueInput!): ProductType
  deleteProductTypes(where: [ProductTypeWhereUniqueInput!]!): [ProductType]
  createProductVariant(data: ProductVariantCreateInput!): ProductVariant
  createProductVariants(data: [ProductVariantCreateInput!]!): [ProductVariant]
  updateProductVariant(where: ProductVariantWhereUniqueInput!, data: ProductVariantUpdateInput!): ProductVariant
  updateProductVariants(data: [ProductVariantUpdateArgs!]!): [ProductVariant]
  deleteProductVariant(where: ProductVariantWhereUniqueInput!): ProductVariant
  deleteProductVariants(where: [ProductVariantWhereUniqueInput!]!): [ProductVariant]
  createRefund(data: RefundCreateInput!): Refund
  createRefunds(data: [RefundCreateInput!]!): [Refund]
  updateRefund(where: RefundWhereUniqueInput!, data: RefundUpdateInput!): Refund
  updateRefunds(data: [RefundUpdateArgs!]!): [Refund]
  deleteRefund(where: RefundWhereUniqueInput!): Refund
  deleteRefunds(where: [RefundWhereUniqueInput!]!): [Refund]
  createRegion(data: RegionCreateInput!): Region
  createRegions(data: [RegionCreateInput!]!): [Region]
  updateRegion(where: RegionWhereUniqueInput!, data: RegionUpdateInput!): Region
  updateRegions(data: [RegionUpdateArgs!]!): [Region]
  deleteRegion(where: RegionWhereUniqueInput!): Region
  deleteRegions(where: [RegionWhereUniqueInput!]!): [Region]
  createReturn(data: ReturnCreateInput!): Return
  createReturns(data: [ReturnCreateInput!]!): [Return]
  updateReturn(where: ReturnWhereUniqueInput!, data: ReturnUpdateInput!): Return
  updateReturns(data: [ReturnUpdateArgs!]!): [Return]
  deleteReturn(where: ReturnWhereUniqueInput!): Return
  deleteReturns(where: [ReturnWhereUniqueInput!]!): [Return]
  createReturnItem(data: ReturnItemCreateInput!): ReturnItem
  createReturnItems(data: [ReturnItemCreateInput!]!): [ReturnItem]
  updateReturnItem(where: ReturnItemWhereUniqueInput!, data: ReturnItemUpdateInput!): ReturnItem
  updateReturnItems(data: [ReturnItemUpdateArgs!]!): [ReturnItem]
  deleteReturnItem(where: ReturnItemWhereUniqueInput!): ReturnItem
  deleteReturnItems(where: [ReturnItemWhereUniqueInput!]!): [ReturnItem]
  createReturnReason(data: ReturnReasonCreateInput!): ReturnReason
  createReturnReasons(data: [ReturnReasonCreateInput!]!): [ReturnReason]
  updateReturnReason(where: ReturnReasonWhereUniqueInput!, data: ReturnReasonUpdateInput!): ReturnReason
  updateReturnReasons(data: [ReturnReasonUpdateArgs!]!): [ReturnReason]
  deleteReturnReason(where: ReturnReasonWhereUniqueInput!): ReturnReason
  deleteReturnReasons(where: [ReturnReasonWhereUniqueInput!]!): [ReturnReason]
  createRole(data: RoleCreateInput!): Role
  createRoles(data: [RoleCreateInput!]!): [Role]
  updateRole(where: RoleWhereUniqueInput!, data: RoleUpdateInput!): Role
  updateRoles(data: [RoleUpdateArgs!]!): [Role]
  deleteRole(where: RoleWhereUniqueInput!): Role
  deleteRoles(where: [RoleWhereUniqueInput!]!): [Role]
  createRuleType(data: RuleTypeCreateInput!): RuleType
  createRuleTypes(data: [RuleTypeCreateInput!]!): [RuleType]
  updateRuleType(where: RuleTypeWhereUniqueInput!, data: RuleTypeUpdateInput!): RuleType
  updateRuleTypes(data: [RuleTypeUpdateArgs!]!): [RuleType]
  deleteRuleType(where: RuleTypeWhereUniqueInput!): RuleType
  deleteRuleTypes(where: [RuleTypeWhereUniqueInput!]!): [RuleType]
  createSalesChannel(data: SalesChannelCreateInput!): SalesChannel
  createSalesChannels(data: [SalesChannelCreateInput!]!): [SalesChannel]
  updateSalesChannel(where: SalesChannelWhereUniqueInput!, data: SalesChannelUpdateInput!): SalesChannel
  updateSalesChannels(data: [SalesChannelUpdateArgs!]!): [SalesChannel]
  deleteSalesChannel(where: SalesChannelWhereUniqueInput!): SalesChannel
  deleteSalesChannels(where: [SalesChannelWhereUniqueInput!]!): [SalesChannel]
  createShippingLabel(data: ShippingLabelCreateInput!): ShippingLabel
  createShippingLabels(data: [ShippingLabelCreateInput!]!): [ShippingLabel]
  updateShippingLabel(where: ShippingLabelWhereUniqueInput!, data: ShippingLabelUpdateInput!): ShippingLabel
  updateShippingLabels(data: [ShippingLabelUpdateArgs!]!): [ShippingLabel]
  deleteShippingLabel(where: ShippingLabelWhereUniqueInput!): ShippingLabel
  deleteShippingLabels(where: [ShippingLabelWhereUniqueInput!]!): [ShippingLabel]
  createShippingMethod(data: ShippingMethodCreateInput!): ShippingMethod
  createShippingMethods(data: [ShippingMethodCreateInput!]!): [ShippingMethod]
  updateShippingMethod(where: ShippingMethodWhereUniqueInput!, data: ShippingMethodUpdateInput!): ShippingMethod
  updateShippingMethods(data: [ShippingMethodUpdateArgs!]!): [ShippingMethod]
  deleteShippingMethod(where: ShippingMethodWhereUniqueInput!): ShippingMethod
  deleteShippingMethods(where: [ShippingMethodWhereUniqueInput!]!): [ShippingMethod]
  createShippingMethodTaxLine(data: ShippingMethodTaxLineCreateInput!): ShippingMethodTaxLine
  createShippingMethodTaxLines(data: [ShippingMethodTaxLineCreateInput!]!): [ShippingMethodTaxLine]
  updateShippingMethodTaxLine(where: ShippingMethodTaxLineWhereUniqueInput!, data: ShippingMethodTaxLineUpdateInput!): ShippingMethodTaxLine
  updateShippingMethodTaxLines(data: [ShippingMethodTaxLineUpdateArgs!]!): [ShippingMethodTaxLine]
  deleteShippingMethodTaxLine(where: ShippingMethodTaxLineWhereUniqueInput!): ShippingMethodTaxLine
  deleteShippingMethodTaxLines(where: [ShippingMethodTaxLineWhereUniqueInput!]!): [ShippingMethodTaxLine]
  createShippingOption(data: ShippingOptionCreateInput!): ShippingOption
  createShippingOptions(data: [ShippingOptionCreateInput!]!): [ShippingOption]
  updateShippingOption(where: ShippingOptionWhereUniqueInput!, data: ShippingOptionUpdateInput!): ShippingOption
  updateShippingOptions(data: [ShippingOptionUpdateArgs!]!): [ShippingOption]
  deleteShippingOption(where: ShippingOptionWhereUniqueInput!): ShippingOption
  deleteShippingOptions(where: [ShippingOptionWhereUniqueInput!]!): [ShippingOption]
  createShippingOptionRequirement(data: ShippingOptionRequirementCreateInput!): ShippingOptionRequirement
  createShippingOptionRequirements(data: [ShippingOptionRequirementCreateInput!]!): [ShippingOptionRequirement]
  updateShippingOptionRequirement(where: ShippingOptionRequirementWhereUniqueInput!, data: ShippingOptionRequirementUpdateInput!): ShippingOptionRequirement
  updateShippingOptionRequirements(data: [ShippingOptionRequirementUpdateArgs!]!): [ShippingOptionRequirement]
  deleteShippingOptionRequirement(where: ShippingOptionRequirementWhereUniqueInput!): ShippingOptionRequirement
  deleteShippingOptionRequirements(where: [ShippingOptionRequirementWhereUniqueInput!]!): [ShippingOptionRequirement]
  createShippingProfile(data: ShippingProfileCreateInput!): ShippingProfile
  createShippingProfiles(data: [ShippingProfileCreateInput!]!): [ShippingProfile]
  updateShippingProfile(where: ShippingProfileWhereUniqueInput!, data: ShippingProfileUpdateInput!): ShippingProfile
  updateShippingProfiles(data: [ShippingProfileUpdateArgs!]!): [ShippingProfile]
  deleteShippingProfile(where: ShippingProfileWhereUniqueInput!): ShippingProfile
  deleteShippingProfiles(where: [ShippingProfileWhereUniqueInput!]!): [ShippingProfile]
  createShippingProvider(data: ShippingProviderCreateInput!): ShippingProvider
  createShippingProviders(data: [ShippingProviderCreateInput!]!): [ShippingProvider]
  updateShippingProvider(where: ShippingProviderWhereUniqueInput!, data: ShippingProviderUpdateInput!): ShippingProvider
  updateShippingProviders(data: [ShippingProviderUpdateArgs!]!): [ShippingProvider]
  deleteShippingProvider(where: ShippingProviderWhereUniqueInput!): ShippingProvider
  deleteShippingProviders(where: [ShippingProviderWhereUniqueInput!]!): [ShippingProvider]
  createStockMovement(data: StockMovementCreateInput!): StockMovement
  createStockMovements(data: [StockMovementCreateInput!]!): [StockMovement]
  updateStockMovement(where: StockMovementWhereUniqueInput!, data: StockMovementUpdateInput!): StockMovement
  updateStockMovements(data: [StockMovementUpdateArgs!]!): [StockMovement]
  deleteStockMovement(where: StockMovementWhereUniqueInput!): StockMovement
  deleteStockMovements(where: [StockMovementWhereUniqueInput!]!): [StockMovement]
  createStore(data: StoreCreateInput!): Store
  createStores(data: [StoreCreateInput!]!): [Store]
  updateStore(where: StoreWhereUniqueInput!, data: StoreUpdateInput!): Store
  updateStores(data: [StoreUpdateArgs!]!): [Store]
  deleteStore(where: StoreWhereUniqueInput!): Store
  deleteStores(where: [StoreWhereUniqueInput!]!): [Store]
  createSwap(data: SwapCreateInput!): Swap
  createSwaps(data: [SwapCreateInput!]!): [Swap]
  updateSwap(where: SwapWhereUniqueInput!, data: SwapUpdateInput!): Swap
  updateSwaps(data: [SwapUpdateArgs!]!): [Swap]
  deleteSwap(where: SwapWhereUniqueInput!): Swap
  deleteSwaps(where: [SwapWhereUniqueInput!]!): [Swap]
  createTaxProvider(data: TaxProviderCreateInput!): TaxProvider
  createTaxProviders(data: [TaxProviderCreateInput!]!): [TaxProvider]
  updateTaxProvider(where: TaxProviderWhereUniqueInput!, data: TaxProviderUpdateInput!): TaxProvider
  updateTaxProviders(data: [TaxProviderUpdateArgs!]!): [TaxProvider]
  deleteTaxProvider(where: TaxProviderWhereUniqueInput!): TaxProvider
  deleteTaxProviders(where: [TaxProviderWhereUniqueInput!]!): [TaxProvider]
  createTaxRate(data: TaxRateCreateInput!): TaxRate
  createTaxRates(data: [TaxRateCreateInput!]!): [TaxRate]
  updateTaxRate(where: TaxRateWhereUniqueInput!, data: TaxRateUpdateInput!): TaxRate
  updateTaxRates(data: [TaxRateUpdateArgs!]!): [TaxRate]
  deleteTaxRate(where: TaxRateWhereUniqueInput!): TaxRate
  deleteTaxRates(where: [TaxRateWhereUniqueInput!]!): [TaxRate]
  createTeam(data: TeamCreateInput!): Team
  createTeams(data: [TeamCreateInput!]!): [Team]
  updateTeam(where: TeamWhereUniqueInput!, data: TeamUpdateInput!): Team
  updateTeams(data: [TeamUpdateArgs!]!): [Team]
  deleteTeam(where: TeamWhereUniqueInput!): Team
  deleteTeams(where: [TeamWhereUniqueInput!]!): [Team]
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createUserField(data: UserFieldCreateInput!): UserField
  createUserFields(data: [UserFieldCreateInput!]!): [UserField]
  updateUserField(where: UserFieldWhereUniqueInput!, data: UserFieldUpdateInput!): UserField
  updateUserFields(data: [UserFieldUpdateArgs!]!): [UserField]
  deleteUserField(where: UserFieldWhereUniqueInput!): UserField
  deleteUserFields(where: [UserFieldWhereUniqueInput!]!): [UserField]
  endSession: Boolean!
  authenticateUserWithPassword(email: String!, password: String!): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
  sendUserPasswordResetLink(email: String!): Boolean!
  redeemUserPasswordResetToken(email: String!, token: String!, password: String!): RedeemUserPasswordResetTokenResult
  updateActiveUser(data: UserUpdateProfileInput!): User
  updateActiveCart(cartId: ID!, data: CartUpdateInput, code: String): Cart
  updateActiveCartLineItem(cartId: ID!, lineId: ID!, quantity: Int!): Cart
  updateActiveUserPassword(oldPassword: String!, newPassword: String!, confirmPassword: String!): User
  updateActiveUserAddress(where: AddressWhereUniqueInput!, data: AddressUpdateInput!): User
  createActiveUserAddress(data: AddressCreateInput!): User
  seedStorefront: Boolean
  deleteActiveUserAddress(where: AddressWhereUniqueInput!): Address
  addDiscountToActiveCart(cartId: ID!, code: String!): Cart
  removeDiscountFromActiveCart(cartId: ID!, code: String!): Cart
  createActiveCartPaymentSessions(cartId: ID!): Cart
  setActiveCartPaymentSession(cartId: ID!, providerId: ID!): Cart
  completeActiveCart(cartId: ID!): JSON
  addActiveCartShippingMethod(cartId: ID!, shippingMethodId: ID!): Cart
  initiatePaymentSession(cartId: ID!, paymentProviderId: String!): PaymentSession
  handlePaymentProviderWebhook(providerId: ID!, event: JSON!, headers: JSON!): WebhookResult!
  getAnalytics: JSON
  importInventory: Boolean
  getRatesForOrder(orderId: ID!, providerId: ID!, dimensions: DimensionsInput): [ShippingRate!]!
  validateShippingAddress(providerId: ID!, address: JSON!): AddressValidationResult!
  trackShipment(providerId: ID!, trackingNumber: String!): ShipmentTrackingResult!
  cancelShippingLabel(providerId: ID!, labelId: ID!): LabelCancellationResult!
  createProviderShippingLabel(orderId: ID!, providerId: ID!, rateId: String!, dimensions: DimensionsInput, lineItems: [LineItemInput!]): ProviderShippingLabel
}

union UserAuthenticationWithPasswordResult = UserAuthenticationWithPasswordSuccess | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  password: String
}

type RedeemUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

enum PasswordResetRedemptionErrorCode {
  FAILURE
  TOKEN_EXPIRED
  TOKEN_REDEEMED
}

type Query {
  address(where: AddressWhereUniqueInput!): Address
  addresses(where: AddressWhereInput! = {}, orderBy: [AddressOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: AddressWhereUniqueInput): [Address!]
  addressesCount(where: AddressWhereInput! = {}): Int
  apiKey(where: ApiKeyWhereUniqueInput!): ApiKey
  apiKeys(where: ApiKeyWhereInput! = {}, orderBy: [ApiKeyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ApiKeyWhereUniqueInput): [ApiKey!]
  apiKeysCount(where: ApiKeyWhereInput! = {}): Int
  batchJob(where: BatchJobWhereUniqueInput!): BatchJob
  batchJobs(where: BatchJobWhereInput! = {}, orderBy: [BatchJobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BatchJobWhereUniqueInput): [BatchJob!]
  batchJobsCount(where: BatchJobWhereInput! = {}): Int
  capture(where: CaptureWhereUniqueInput!): Capture
  captures(where: CaptureWhereInput! = {}, orderBy: [CaptureOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CaptureWhereUniqueInput): [Capture!]
  capturesCount(where: CaptureWhereInput! = {}): Int
  cart(where: CartWhereUniqueInput!): Cart
  carts(where: CartWhereInput! = {}, orderBy: [CartOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CartWhereUniqueInput): [Cart!]
  cartsCount(where: CartWhereInput! = {}): Int
  claimImage(where: ClaimImageWhereUniqueInput!): ClaimImage
  claimImages(where: ClaimImageWhereInput! = {}, orderBy: [ClaimImageOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimImageWhereUniqueInput): [ClaimImage!]
  claimImagesCount(where: ClaimImageWhereInput! = {}): Int
  claimItem(where: ClaimItemWhereUniqueInput!): ClaimItem
  claimItems(where: ClaimItemWhereInput! = {}, orderBy: [ClaimItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimItemWhereUniqueInput): [ClaimItem!]
  claimItemsCount(where: ClaimItemWhereInput! = {}): Int
  claimOrder(where: ClaimOrderWhereUniqueInput!): ClaimOrder
  claimOrders(where: ClaimOrderWhereInput! = {}, orderBy: [ClaimOrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimOrderWhereUniqueInput): [ClaimOrder!]
  claimOrdersCount(where: ClaimOrderWhereInput! = {}): Int
  claimTag(where: ClaimTagWhereUniqueInput!): ClaimTag
  claimTags(where: ClaimTagWhereInput! = {}, orderBy: [ClaimTagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ClaimTagWhereUniqueInput): [ClaimTag!]
  claimTagsCount(where: ClaimTagWhereInput! = {}): Int
  country(where: CountryWhereUniqueInput!): Country
  countries(where: CountryWhereInput! = {}, orderBy: [CountryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CountryWhereUniqueInput): [Country!]
  countriesCount(where: CountryWhereInput! = {}): Int
  currency(where: CurrencyWhereUniqueInput!): Currency
  currencies(where: CurrencyWhereInput! = {}, orderBy: [CurrencyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CurrencyWhereUniqueInput): [Currency!]
  currenciesCount(where: CurrencyWhereInput! = {}): Int
  customShippingOption(where: CustomShippingOptionWhereUniqueInput!): CustomShippingOption
  customShippingOptions(where: CustomShippingOptionWhereInput! = {}, orderBy: [CustomShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomShippingOptionWhereUniqueInput): [CustomShippingOption!]
  customShippingOptionsCount(where: CustomShippingOptionWhereInput! = {}): Int
  customerGroup(where: CustomerGroupWhereUniqueInput!): CustomerGroup
  customerGroups(where: CustomerGroupWhereInput! = {}, orderBy: [CustomerGroupOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CustomerGroupWhereUniqueInput): [CustomerGroup!]
  customerGroupsCount(where: CustomerGroupWhereInput! = {}): Int
  discount(where: DiscountWhereUniqueInput!): Discount
  discounts(where: DiscountWhereInput! = {}, orderBy: [DiscountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountWhereUniqueInput): [Discount!]
  discountsCount(where: DiscountWhereInput! = {}): Int
  discountCondition(where: DiscountConditionWhereUniqueInput!): DiscountCondition
  discountConditions(where: DiscountConditionWhereInput! = {}, orderBy: [DiscountConditionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountConditionWhereUniqueInput): [DiscountCondition!]
  discountConditionsCount(where: DiscountConditionWhereInput! = {}): Int
  discountRule(where: DiscountRuleWhereUniqueInput!): DiscountRule
  discountRules(where: DiscountRuleWhereInput! = {}, orderBy: [DiscountRuleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DiscountRuleWhereUniqueInput): [DiscountRule!]
  discountRulesCount(where: DiscountRuleWhereInput! = {}): Int
  draftOrder(where: DraftOrderWhereUniqueInput!): DraftOrder
  draftOrders(where: DraftOrderWhereInput! = {}, orderBy: [DraftOrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: DraftOrderWhereUniqueInput): [DraftOrder!]
  draftOrdersCount(where: DraftOrderWhereInput! = {}): Int
  fulfillment(where: FulfillmentWhereUniqueInput!): Fulfillment
  fulfillments(where: FulfillmentWhereInput! = {}, orderBy: [FulfillmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentWhereUniqueInput): [Fulfillment!]
  fulfillmentsCount(where: FulfillmentWhereInput! = {}): Int
  fulfillmentItem(where: FulfillmentItemWhereUniqueInput!): FulfillmentItem
  fulfillmentItems(where: FulfillmentItemWhereInput! = {}, orderBy: [FulfillmentItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentItemWhereUniqueInput): [FulfillmentItem!]
  fulfillmentItemsCount(where: FulfillmentItemWhereInput! = {}): Int
  fulfillmentProvider(where: FulfillmentProviderWhereUniqueInput!): FulfillmentProvider
  fulfillmentProviders(where: FulfillmentProviderWhereInput! = {}, orderBy: [FulfillmentProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: FulfillmentProviderWhereUniqueInput): [FulfillmentProvider!]
  fulfillmentProvidersCount(where: FulfillmentProviderWhereInput! = {}): Int
  giftCard(where: GiftCardWhereUniqueInput!): GiftCard
  giftCards(where: GiftCardWhereInput! = {}, orderBy: [GiftCardOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardWhereUniqueInput): [GiftCard!]
  giftCardsCount(where: GiftCardWhereInput! = {}): Int
  giftCardTransaction(where: GiftCardTransactionWhereUniqueInput!): GiftCardTransaction
  giftCardTransactions(where: GiftCardTransactionWhereInput! = {}, orderBy: [GiftCardTransactionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: GiftCardTransactionWhereUniqueInput): [GiftCardTransaction!]
  giftCardTransactionsCount(where: GiftCardTransactionWhereInput! = {}): Int
  idempotencyKey(where: IdempotencyKeyWhereUniqueInput!): IdempotencyKey
  idempotencyKeys(where: IdempotencyKeyWhereInput! = {}, orderBy: [IdempotencyKeyOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: IdempotencyKeyWhereUniqueInput): [IdempotencyKey!]
  idempotencyKeysCount(where: IdempotencyKeyWhereInput! = {}): Int
  invite(where: InviteWhereUniqueInput!): Invite
  invites(where: InviteWhereInput! = {}, orderBy: [InviteOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: InviteWhereUniqueInput): [Invite!]
  invitesCount(where: InviteWhereInput! = {}): Int
  lineItem(where: LineItemWhereUniqueInput!): LineItem
  lineItems(where: LineItemWhereInput! = {}, orderBy: [LineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemWhereUniqueInput): [LineItem!]
  lineItemsCount(where: LineItemWhereInput! = {}): Int
  lineItemAdjustment(where: LineItemAdjustmentWhereUniqueInput!): LineItemAdjustment
  lineItemAdjustments(where: LineItemAdjustmentWhereInput! = {}, orderBy: [LineItemAdjustmentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemAdjustmentWhereUniqueInput): [LineItemAdjustment!]
  lineItemAdjustmentsCount(where: LineItemAdjustmentWhereInput! = {}): Int
  lineItemTaxLine(where: LineItemTaxLineWhereUniqueInput!): LineItemTaxLine
  lineItemTaxLines(where: LineItemTaxLineWhereInput! = {}, orderBy: [LineItemTaxLineOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LineItemTaxLineWhereUniqueInput): [LineItemTaxLine!]
  lineItemTaxLinesCount(where: LineItemTaxLineWhereInput! = {}): Int
  location(where: LocationWhereUniqueInput!): Location
  locations(where: LocationWhereInput! = {}, orderBy: [LocationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LocationWhereUniqueInput): [Location!]
  locationsCount(where: LocationWhereInput! = {}): Int
  measurement(where: MeasurementWhereUniqueInput!): Measurement
  measurements(where: MeasurementWhereInput! = {}, orderBy: [MeasurementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MeasurementWhereUniqueInput): [Measurement!]
  measurementsCount(where: MeasurementWhereInput! = {}): Int
  moneyAmount(where: MoneyAmountWhereUniqueInput!): MoneyAmount
  moneyAmounts(where: MoneyAmountWhereInput! = {}, orderBy: [MoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MoneyAmountWhereUniqueInput): [MoneyAmount!]
  moneyAmountsCount(where: MoneyAmountWhereInput! = {}): Int
  note(where: NoteWhereUniqueInput!): Note
  notes(where: NoteWhereInput! = {}, orderBy: [NoteOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NoteWhereUniqueInput): [Note!]
  notesCount(where: NoteWhereInput! = {}): Int
  notification(where: NotificationWhereUniqueInput!): Notification
  notifications(where: NotificationWhereInput! = {}, orderBy: [NotificationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NotificationWhereUniqueInput): [Notification!]
  notificationsCount(where: NotificationWhereInput! = {}): Int
  notificationProvider(where: NotificationProviderWhereUniqueInput!): NotificationProvider
  notificationProviders(where: NotificationProviderWhereInput! = {}, orderBy: [NotificationProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: NotificationProviderWhereUniqueInput): [NotificationProvider!]
  notificationProvidersCount(where: NotificationProviderWhereInput! = {}): Int
  oAuth(where: OAuthWhereUniqueInput!): OAuth
  oAuths(where: OAuthWhereInput! = {}, orderBy: [OAuthOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OAuthWhereUniqueInput): [OAuth!]
  oAuthsCount(where: OAuthWhereInput! = {}): Int
  order(where: OrderWhereUniqueInput!): Order
  orders(where: OrderWhereInput! = {}, orderBy: [OrderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderWhereUniqueInput): [Order!]
  ordersCount(where: OrderWhereInput! = {}): Int
  orderEvent(where: OrderEventWhereUniqueInput!): OrderEvent
  orderEvents(where: OrderEventWhereInput! = {}, orderBy: [OrderEventOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderEventWhereUniqueInput): [OrderEvent!]
  orderEventsCount(where: OrderEventWhereInput! = {}): Int
  orderLineItem(where: OrderLineItemWhereUniqueInput!): OrderLineItem
  orderLineItems(where: OrderLineItemWhereInput! = {}, orderBy: [OrderLineItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderLineItemWhereUniqueInput): [OrderLineItem!]
  orderLineItemsCount(where: OrderLineItemWhereInput! = {}): Int
  orderMoneyAmount(where: OrderMoneyAmountWhereUniqueInput!): OrderMoneyAmount
  orderMoneyAmounts(where: OrderMoneyAmountWhereInput! = {}, orderBy: [OrderMoneyAmountOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: OrderMoneyAmountWhereUniqueInput): [OrderMoneyAmount!]
  orderMoneyAmountsCount(where: OrderMoneyAmountWhereInput! = {}): Int
  payment(where: PaymentWhereUniqueInput!): Payment
  payments(where: PaymentWhereInput! = {}, orderBy: [PaymentOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentWhereUniqueInput): [Payment!]
  paymentsCount(where: PaymentWhereInput! = {}): Int
  paymentCollection(where: PaymentCollectionWhereUniqueInput!): PaymentCollection
  paymentCollections(where: PaymentCollectionWhereInput! = {}, orderBy: [PaymentCollectionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentCollectionWhereUniqueInput): [PaymentCollection!]
  paymentCollectionsCount(where: PaymentCollectionWhereInput! = {}): Int
  paymentProvider(where: PaymentProviderWhereUniqueInput!): PaymentProvider
  paymentProviders(where: PaymentProviderWhereInput! = {}, orderBy: [PaymentProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentProviderWhereUniqueInput): [PaymentProvider!]
  paymentProvidersCount(where: PaymentProviderWhereInput! = {}): Int
  paymentSession(where: PaymentSessionWhereUniqueInput!): PaymentSession
  paymentSessions(where: PaymentSessionWhereInput! = {}, orderBy: [PaymentSessionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PaymentSessionWhereUniqueInput): [PaymentSession!]
  paymentSessionsCount(where: PaymentSessionWhereInput! = {}): Int
  priceList(where: PriceListWhereUniqueInput!): PriceList
  priceLists(where: PriceListWhereInput! = {}, orderBy: [PriceListOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceListWhereUniqueInput): [PriceList!]
  priceListsCount(where: PriceListWhereInput! = {}): Int
  priceRule(where: PriceRuleWhereUniqueInput!): PriceRule
  priceRules(where: PriceRuleWhereInput! = {}, orderBy: [PriceRuleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceRuleWhereUniqueInput): [PriceRule!]
  priceRulesCount(where: PriceRuleWhereInput! = {}): Int
  priceSet(where: PriceSetWhereUniqueInput!): PriceSet
  priceSets(where: PriceSetWhereInput! = {}, orderBy: [PriceSetOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: PriceSetWhereUniqueInput): [PriceSet!]
  priceSetsCount(where: PriceSetWhereInput! = {}): Int
  product(where: ProductWhereUniqueInput!): Product
  products(where: ProductWhereInput! = {}, orderBy: [ProductOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductWhereUniqueInput): [Product!]
  productsCount(where: ProductWhereInput! = {}): Int
  productCategory(where: ProductCategoryWhereUniqueInput!): ProductCategory
  productCategories(where: ProductCategoryWhereInput! = {}, orderBy: [ProductCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCategoryWhereUniqueInput): [ProductCategory!]
  productCategoriesCount(where: ProductCategoryWhereInput! = {}): Int
  productCollection(where: ProductCollectionWhereUniqueInput!): ProductCollection
  productCollections(where: ProductCollectionWhereInput! = {}, orderBy: [ProductCollectionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductCollectionWhereUniqueInput): [ProductCollection!]
  productCollectionsCount(where: ProductCollectionWhereInput! = {}): Int
  productImage(where: ProductImageWhereUniqueInput!): ProductImage
  productImages(where: ProductImageWhereInput! = {}, orderBy: [ProductImageOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductImageWhereUniqueInput): [ProductImage!]
  productImagesCount(where: ProductImageWhereInput! = {}): Int
  productOption(where: ProductOptionWhereUniqueInput!): ProductOption
  productOptions(where: ProductOptionWhereInput! = {}, orderBy: [ProductOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductOptionWhereUniqueInput): [ProductOption!]
  productOptionsCount(where: ProductOptionWhereInput! = {}): Int
  productOptionValue(where: ProductOptionValueWhereUniqueInput!): ProductOptionValue
  productOptionValues(where: ProductOptionValueWhereInput! = {}, orderBy: [ProductOptionValueOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductOptionValueWhereUniqueInput): [ProductOptionValue!]
  productOptionValuesCount(where: ProductOptionValueWhereInput! = {}): Int
  productTag(where: ProductTagWhereUniqueInput!): ProductTag
  productTags(where: ProductTagWhereInput! = {}, orderBy: [ProductTagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTagWhereUniqueInput): [ProductTag!]
  productTagsCount(where: ProductTagWhereInput! = {}): Int
  productType(where: ProductTypeWhereUniqueInput!): ProductType
  productTypes(where: ProductTypeWhereInput! = {}, orderBy: [ProductTypeOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductTypeWhereUniqueInput): [ProductType!]
  productTypesCount(where: ProductTypeWhereInput! = {}): Int
  productVariant(where: ProductVariantWhereUniqueInput!): ProductVariant
  productVariants(where: ProductVariantWhereInput! = {}, orderBy: [ProductVariantOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ProductVariantWhereUniqueInput): [ProductVariant!]
  productVariantsCount(where: ProductVariantWhereInput! = {}): Int
  refund(where: RefundWhereUniqueInput!): Refund
  refunds(where: RefundWhereInput! = {}, orderBy: [RefundOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RefundWhereUniqueInput): [Refund!]
  refundsCount(where: RefundWhereInput! = {}): Int
  region(where: RegionWhereUniqueInput!): Region
  regions(where: RegionWhereInput! = {}, orderBy: [RegionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RegionWhereUniqueInput): [Region!]
  regionsCount(where: RegionWhereInput! = {}): Int
  return(where: ReturnWhereUniqueInput!): Return
  returns(where: ReturnWhereInput! = {}, orderBy: [ReturnOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnWhereUniqueInput): [Return!]
  returnsCount(where: ReturnWhereInput! = {}): Int
  returnItem(where: ReturnItemWhereUniqueInput!): ReturnItem
  returnItems(where: ReturnItemWhereInput! = {}, orderBy: [ReturnItemOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnItemWhereUniqueInput): [ReturnItem!]
  returnItemsCount(where: ReturnItemWhereInput! = {}): Int
  returnReason(where: ReturnReasonWhereUniqueInput!): ReturnReason
  returnReasons(where: ReturnReasonWhereInput! = {}, orderBy: [ReturnReasonOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ReturnReasonWhereUniqueInput): [ReturnReason!]
  returnReasonsCount(where: ReturnReasonWhereInput! = {}): Int
  role(where: RoleWhereUniqueInput!): Role
  roles(where: RoleWhereInput! = {}, orderBy: [RoleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RoleWhereUniqueInput): [Role!]
  rolesCount(where: RoleWhereInput! = {}): Int
  ruleType(where: RuleTypeWhereUniqueInput!): RuleType
  ruleTypes(where: RuleTypeWhereInput! = {}, orderBy: [RuleTypeOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RuleTypeWhereUniqueInput): [RuleType!]
  ruleTypesCount(where: RuleTypeWhereInput! = {}): Int
  salesChannel(where: SalesChannelWhereUniqueInput!): SalesChannel
  salesChannels(where: SalesChannelWhereInput! = {}, orderBy: [SalesChannelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SalesChannelWhereUniqueInput): [SalesChannel!]
  salesChannelsCount(where: SalesChannelWhereInput! = {}): Int
  shippingLabel(where: ShippingLabelWhereUniqueInput!): ShippingLabel
  shippingLabels(where: ShippingLabelWhereInput! = {}, orderBy: [ShippingLabelOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingLabelWhereUniqueInput): [ShippingLabel!]
  shippingLabelsCount(where: ShippingLabelWhereInput! = {}): Int
  shippingMethod(where: ShippingMethodWhereUniqueInput!): ShippingMethod
  shippingMethods(where: ShippingMethodWhereInput! = {}, orderBy: [ShippingMethodOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodWhereUniqueInput): [ShippingMethod!]
  shippingMethodsCount(where: ShippingMethodWhereInput! = {}): Int
  shippingMethodTaxLine(where: ShippingMethodTaxLineWhereUniqueInput!): ShippingMethodTaxLine
  shippingMethodTaxLines(where: ShippingMethodTaxLineWhereInput! = {}, orderBy: [ShippingMethodTaxLineOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingMethodTaxLineWhereUniqueInput): [ShippingMethodTaxLine!]
  shippingMethodTaxLinesCount(where: ShippingMethodTaxLineWhereInput! = {}): Int
  shippingOption(where: ShippingOptionWhereUniqueInput!): ShippingOption
  shippingOptions(where: ShippingOptionWhereInput! = {}, orderBy: [ShippingOptionOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionWhereUniqueInput): [ShippingOption!]
  shippingOptionsCount(where: ShippingOptionWhereInput! = {}): Int
  shippingOptionRequirement(where: ShippingOptionRequirementWhereUniqueInput!): ShippingOptionRequirement
  shippingOptionRequirements(where: ShippingOptionRequirementWhereInput! = {}, orderBy: [ShippingOptionRequirementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingOptionRequirementWhereUniqueInput): [ShippingOptionRequirement!]
  shippingOptionRequirementsCount(where: ShippingOptionRequirementWhereInput! = {}): Int
  shippingProfile(where: ShippingProfileWhereUniqueInput!): ShippingProfile
  shippingProfiles(where: ShippingProfileWhereInput! = {}, orderBy: [ShippingProfileOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingProfileWhereUniqueInput): [ShippingProfile!]
  shippingProfilesCount(where: ShippingProfileWhereInput! = {}): Int
  shippingProvider(where: ShippingProviderWhereUniqueInput!): ShippingProvider
  shippingProviders(where: ShippingProviderWhereInput! = {}, orderBy: [ShippingProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ShippingProviderWhereUniqueInput): [ShippingProvider!]
  shippingProvidersCount(where: ShippingProviderWhereInput! = {}): Int
  stockMovement(where: StockMovementWhereUniqueInput!): StockMovement
  stockMovements(where: StockMovementWhereInput! = {}, orderBy: [StockMovementOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: StockMovementWhereUniqueInput): [StockMovement!]
  stockMovementsCount(where: StockMovementWhereInput! = {}): Int
  store(where: StoreWhereUniqueInput!): Store
  stores(where: StoreWhereInput! = {}, orderBy: [StoreOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: StoreWhereUniqueInput): [Store!]
  storesCount(where: StoreWhereInput! = {}): Int
  swap(where: SwapWhereUniqueInput!): Swap
  swaps(where: SwapWhereInput! = {}, orderBy: [SwapOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SwapWhereUniqueInput): [Swap!]
  swapsCount(where: SwapWhereInput! = {}): Int
  taxProvider(where: TaxProviderWhereUniqueInput!): TaxProvider
  taxProviders(where: TaxProviderWhereInput! = {}, orderBy: [TaxProviderOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxProviderWhereUniqueInput): [TaxProvider!]
  taxProvidersCount(where: TaxProviderWhereInput! = {}): Int
  taxRate(where: TaxRateWhereUniqueInput!): TaxRate
  taxRates(where: TaxRateWhereInput! = {}, orderBy: [TaxRateOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TaxRateWhereUniqueInput): [TaxRate!]
  taxRatesCount(where: TaxRateWhereInput! = {}): Int
  team(where: TeamWhereUniqueInput!): Team
  teams(where: TeamWhereInput! = {}, orderBy: [TeamOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TeamWhereUniqueInput): [Team!]
  teamsCount(where: TeamWhereInput! = {}): Int
  user(where: UserWhereUniqueInput!): User
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  usersCount(where: UserWhereInput! = {}): Int
  userField(where: UserFieldWhereUniqueInput!): UserField
  userFields(where: UserFieldWhereInput! = {}, orderBy: [UserFieldOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserFieldWhereUniqueInput): [UserField!]
  userFieldsCount(where: UserFieldWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
  validateUserPasswordResetToken(email: String!, token: String!): ValidateUserPasswordResetTokenResult
  redirectToInit: Boolean
  activeCart(cartId: ID!): JSON
  activeCartShippingOptions(cartId: ID!): [ShippingOption!]
  activeCartPaymentProviders(regionId: ID!): [PaymentProvider!]
  activeCartRegion(countryCode: String!): Region
  getCustomerOrder(orderId: ID!, secretKey: String): JSON
  getCustomerOrders(limit: Int, offset: Int): JSON
  getAnalytics(timeframe: String): JSON
}

union AuthenticatedItem = User

type ValidateUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  graphql: KeystoneAdminUIGraphQL!
  initialColumns: [String!]!
  initialSearchFields: [String!]!
  initialSort: KeystoneAdminUISort
  isSingleton: Boolean!
  hideCreate: Boolean!
  hideDelete: Boolean!
  isHidden: Boolean!
  itemQueryName: String!
  listQueryName: String!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUIGraphQL {
  names: KeystoneAdminUIGraphQLNames!
}

type KeystoneAdminUIGraphQLNames {
  outputTypeName: String!
  whereInputName: String!
  whereUniqueInputName: String!
  createInputName: String!
  createMutationName: String!
  createManyMutationName: String!
  relateToOneForCreateInputName: String!
  relateToManyForCreateInputName: String!
  itemQueryName: String!
  listOrderName: String!
  listQueryCountName: String!
  listQueryName: String!
  updateInputName: String!
  updateMutationName: String!
  updateManyInputName: String!
  updateManyMutationName: String!
  relateToOneForUpdateInputName: String!
  relateToManyForUpdateInputName: String!
  deleteMutationName: String!
  deleteManyMutationName: String!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}

input CartCodeInput {
  code: String!
}

type ShippingRate {
  id: ID!
  provider: String!
  service: String!
  carrier: String!
  price: String!
  estimatedDays: String!
}

type ProviderShippingLabel {
  id: ID!
  status: String!
  trackingNumber: String
  trackingUrl: String
  labelUrl: String
  data: JSON
}

type PackageDimensions {
  length: Float!
  width: Float!
  height: Float!
  weight: Float!
  unit: String!
  weightUnit: String!
}

input DimensionsInput {
  length: Float!
  width: Float!
  height: Float!
  weight: Float!
  unit: String!
  weightUnit: String!
}

input LineItemInput {
  lineItemId: ID!
  quantity: Int!
}

type AddressValidationResult {
  isValid: Boolean!
  normalizedAddress: JSON
  errors: [String!]
}

type TrackingEvent {
  status: String!
  location: String
  timestamp: String!
  message: String
}

type ShipmentTrackingResult {
  status: String!
  estimatedDeliveryDate: String
  trackingEvents: [TrackingEvent!]!
}

type LabelCancellationResult {
  success: Boolean!
  refundStatus: String
  error: String
}

input UserUpdateProfileInput {
  email: String
  name: String
  phone: String
  billingAddress: String
  password: String
  onboardingStatus: String
}

type WebhookResult {
  success: Boolean!
}
