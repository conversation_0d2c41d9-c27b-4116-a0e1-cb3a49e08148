// This file is automatically generated by Keystone, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource postgresql {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "postgresql"
}

generator client {
  provider = "prisma-client-js"
}

model Address {
  id                           String             @id @default(cuid())
  company                      String             @default("")
  firstName                    String             @default("")
  lastName                     String             @default("")
  address1                     String             @default("")
  address2                     String             @default("")
  city                         String             @default("")
  province                     String             @default("")
  postalCode                   String             @default("")
  phone                        String             @default("")
  isBilling                    Boolean            @default(false)
  metadata                     Json?
  country                      Country?           @relation("Address_country", fields: [countryId], references: [id])
  countryId                    String?            @map("country")
  user                         User?              @relation("Address_user", fields: [userId], references: [id])
  userId                       String?            @map("user")
  shippingProviders            ShippingProvider[] @relation("ShippingProvider_fromAddress")
  cart                         Cart?              @relation("Address_cart", fields: [cartId], references: [id])
  cartId                       String?            @map("cart")
  claimOrders                  ClaimOrder[]       @relation("ClaimOrder_address")
  ordersUsingAsBillingAddress  Order[]            @relation("Order_billingAddress")
  ordersUsingAsShippingAddress Order[]            @relation("Order_shippingAddress")
  cartsUsingAsBillingAddress   Cart[]             @relation("Cart_billingAddress")
  cartsUsingAsShippingAddress  Cart[]             @relation("Cart_shippingAddress")
  swaps                        Swap[]             @relation("Swap_address")
  createdAt                    DateTime           @default(now())
  updatedAt                    DateTime           @default(now()) @updatedAt

  @@index([countryId])
  @@index([userId])
  @@index([cartId])
}

model ApiKey {
  id        String   @id @default(cuid())
  user      User?    @relation("ApiKey_user", fields: [userId], references: [id])
  userId    String?  @map("user")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([userId])
}

model BatchJob {
  id          String             @id @default(cuid())
  type        BatchJobTypeType
  status      BatchJobStatusType @default(CREATED)
  context     Json?              @default("{}")
  result      Json?              @default("{}")
  error       String             @default("")
  progress    Int?               @default(0)
  createdBy   User?              @relation("BatchJob_createdBy", fields: [createdById], references: [id])
  createdById String?            @map("createdBy")
  completedAt DateTime?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @default(now()) @updatedAt

  @@index([createdById])
}

model Capture {
  id        String   @id @default(cuid())
  amount    Int
  payment   Payment? @relation("Capture_payment", fields: [paymentId], references: [id])
  paymentId String?  @map("payment")
  metadata  Json?
  createdBy String   @default("")
  createdAt DateTime @default(now())
  updatedAt DateTime @default(now()) @updatedAt

  @@index([paymentId])
}

model Cart {
  id                    String                 @id @default(cuid())
  email                 String                 @default("")
  type                  CartTypeType           @default(default)
  metadata              Json?
  idempotencyKey        String                 @default("")
  context               Json?
  paymentAuthorizedAt   DateTime?
  abandonedEmailSent    Boolean                @default(false)
  user                  User?                  @relation("Cart_user", fields: [userId], references: [id])
  userId                String?                @map("user")
  region                Region?                @relation("Cart_region", fields: [regionId], references: [id])
  regionId              String?                @map("region")
  addresses             Address[]              @relation("Address_cart")
  discounts             Discount[]             @relation("Cart_discounts")
  giftCards             GiftCard[]             @relation("Cart_giftCards")
  draftOrder            DraftOrder?            @relation("Cart_draftOrder", fields: [draftOrderId], references: [id])
  draftOrderId          String?                @unique @map("draftOrder")
  order                 Order?                 @relation("Cart_order", fields: [orderId], references: [id])
  orderId               String?                @unique @map("order")
  lineItems             LineItem[]             @relation("LineItem_cart")
  customShippingOptions CustomShippingOption[] @relation("CustomShippingOption_cart")
  swap                  Swap?                  @relation("Cart_swap", fields: [swapId], references: [id])
  swapId                String?                @unique @map("swap")
  shippingMethods       ShippingMethod[]       @relation("ShippingMethod_cart")
  payment               Payment?               @relation("Cart_payment", fields: [paymentId], references: [id])
  paymentId             String?                @unique @map("payment")
  paymentCollection     PaymentCollection?     @relation("Cart_paymentCollection", fields: [paymentCollectionId], references: [id])
  paymentCollectionId   String?                @unique @map("paymentCollection")
  billingAddress        Address?               @relation("Cart_billingAddress", fields: [billingAddressId], references: [id])
  billingAddressId      String?                @map("billingAddress")
  shippingAddress       Address?               @relation("Cart_shippingAddress", fields: [shippingAddressId], references: [id])
  shippingAddressId     String?                @map("shippingAddress")
  createdAt             DateTime               @default(now())
  updatedAt             DateTime               @default(now()) @updatedAt

  @@index([userId])
  @@index([regionId])
  @@index([billingAddressId])
  @@index([shippingAddressId])
}

model ClaimImage {
  id              String     @id @default(cuid())
  image_id        String?
  image_filesize  Int?
  image_width     Int?
  image_height    Int?
  image_extension String?
  altText         String     @default("")
  claimItem       ClaimItem? @relation("ClaimImage_claimItem", fields: [claimItemId], references: [id])
  claimItemId     String?    @map("claimItem")
  metadata        Json?
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @default(now()) @updatedAt

  @@index([claimItemId])
}

model ClaimItem {
  id               String              @id @default(cuid())
  reason           ClaimItemReasonType
  note             String              @default("")
  quantity         Int
  metadata         Json?
  productVariant   ProductVariant?     @relation("ClaimItem_productVariant", fields: [productVariantId], references: [id])
  productVariantId String?             @map("productVariant")
  lineItem         LineItem?           @relation("ClaimItem_lineItem", fields: [lineItemId], references: [id])
  lineItemId       String?             @map("lineItem")
  claimOrder       ClaimOrder?         @relation("ClaimItem_claimOrder", fields: [claimOrderId], references: [id])
  claimOrderId     String?             @map("claimOrder")
  claimImages      ClaimImage[]        @relation("ClaimImage_claimItem")
  claimTags        ClaimTag[]          @relation("ClaimItem_claimTags")
  createdAt        DateTime            @default(now())
  updatedAt        DateTime            @default(now()) @updatedAt

  @@index([productVariantId])
  @@index([lineItemId])
  @@index([claimOrderId])
}

model ClaimOrder {
  id                String                          @id @default(cuid())
  paymentStatus     ClaimOrderPaymentStatusType     @default(na)
  fulfillmentStatus ClaimOrderFulfillmentStatusType @default(not_fulfilled)
  type              ClaimOrderTypeType
  refundAmount      Int?
  canceledAt        DateTime?
  metadata          Json?
  idempotencyKey    String                          @default("")
  noNotification    Boolean                         @default(false)
  address           Address?                        @relation("ClaimOrder_address", fields: [addressId], references: [id])
  addressId         String?                         @map("address")
  order             Order?                          @relation("ClaimOrder_order", fields: [orderId], references: [id])
  orderId           String?                         @map("order")
  claimItems        ClaimItem[]                     @relation("ClaimItem_claimOrder")
  fulfillments      Fulfillment[]                   @relation("Fulfillment_claimOrder")
  lineItems         LineItem[]                      @relation("LineItem_claimOrder")
  return            Return?                         @relation("ClaimOrder_return", fields: [returnId], references: [id])
  returnId          String?                         @unique @map("return")
  shippingMethods   ShippingMethod[]                @relation("ShippingMethod_claimOrder")
  createdAt         DateTime                        @default(now())
  updatedAt         DateTime                        @default(now()) @updatedAt

  @@index([addressId])
  @@index([orderId])
}

model ClaimTag {
  id         String      @id @default(cuid())
  value      String      @default("")
  metadata   Json?
  claimItems ClaimItem[] @relation("ClaimItem_claimTags")
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @default(now()) @updatedAt
}

model Country {
  id          String    @id @default(cuid())
  iso2        String    @unique @default("")
  iso3        String    @default("")
  numCode     Int
  name        String    @default("")
  displayName String    @default("")
  region      Region?   @relation("Country_region", fields: [regionId], references: [id])
  regionId    String?   @map("region")
  addresses   Address[] @relation("Address_country")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now()) @updatedAt

  @@index([regionId])
}

model Currency {
  id                             String             @id @default(cuid())
  code                           String             @unique @default("")
  symbol                         String             @default("")
  symbolNative                   String             @default("")
  name                           String             @default("")
  moneyAmounts                   MoneyAmount[]      @relation("MoneyAmount_currency")
  orders                         Order[]            @relation("Order_currency")
  payments                       Payment[]          @relation("Payment_currency")
  regions                        Region[]           @relation("Region_currency")
  stores                         Store[]            @relation("Currency_stores")
  createdAt                      DateTime           @default(now())
  updatedAt                      DateTime           @default(now()) @updatedAt
  from_OrderMoneyAmount_currency OrderMoneyAmount[] @relation("OrderMoneyAmount_currency")
}

model CustomShippingOption {
  id               String          @id @default(cuid())
  price            Int
  metadata         Json?
  shippingOption   ShippingOption? @relation("CustomShippingOption_shippingOption", fields: [shippingOptionId], references: [id])
  shippingOptionId String?         @map("shippingOption")
  cart             Cart?           @relation("CustomShippingOption_cart", fields: [cartId], references: [id])
  cartId           String?         @map("cart")
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @default(now()) @updatedAt

  @@index([shippingOptionId])
  @@index([cartId])
}

model CustomerGroup {
  id                 String              @id @default(cuid())
  name               String              @default("")
  metadata           Json?
  users              User[]              @relation("CustomerGroup_users")
  discountConditions DiscountCondition[] @relation("CustomerGroup_discountConditions")
  priceLists         PriceList[]         @relation("CustomerGroup_priceLists")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt
}

model Discount {
  id                  String               @id @default(cuid())
  code                String               @unique @default("")
  isDynamic           Boolean              @default(false)
  isDisabled          Boolean              @default(false)
  stackable           Boolean              @default(false)
  startsAt            DateTime             @default(now())
  endsAt              DateTime?
  metadata            Json?
  usageLimit          Int?
  usageCount          Int                  @default(0)
  validDuration       String               @default("")
  discountRule        DiscountRule?        @relation("Discount_discountRule", fields: [discountRuleId], references: [id])
  discountRuleId      String?              @map("discountRule")
  carts               Cart[]               @relation("Cart_discounts")
  lineItemAdjustments LineItemAdjustment[] @relation("LineItemAdjustment_discount")
  regions             Region[]             @relation("Discount_regions")
  orders              Order[]              @relation("Discount_orders")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt

  @@index([discountRuleId])
}

model DiscountCondition {
  id                 String                        @id @default(cuid())
  type               DiscountConditionTypeType
  operator           DiscountConditionOperatorType
  metadata           Json?
  discountRule       DiscountRule?                 @relation("DiscountCondition_discountRule", fields: [discountRuleId], references: [id])
  discountRuleId     String?                       @map("discountRule")
  customerGroups     CustomerGroup[]               @relation("CustomerGroup_discountConditions")
  products           Product[]                     @relation("DiscountCondition_products")
  productCollections ProductCollection[]           @relation("DiscountCondition_productCollections")
  productCategories  ProductCategory[]             @relation("DiscountCondition_productCategories")
  productTags        ProductTag[]                  @relation("DiscountCondition_productTags")
  productTypes       ProductType[]                 @relation("DiscountCondition_productTypes")
  createdAt          DateTime                      @default(now())
  updatedAt          DateTime                      @default(now()) @updatedAt

  @@index([discountRuleId])
}

model DiscountRule {
  id                 String                      @id @default(cuid())
  description        String                      @default("")
  type               DiscountRuleTypeType
  value              Int
  allocation         DiscountRuleAllocationType?
  metadata           Json?
  discounts          Discount[]                  @relation("Discount_discountRule")
  discountConditions DiscountCondition[]         @relation("DiscountCondition_discountRule")
  products           Product[]                   @relation("DiscountRule_products")
  createdAt          DateTime                    @default(now())
  updatedAt          DateTime                    @default(now()) @updatedAt
}

model DraftOrder {
  id                  String               @id @default(cuid())
  status              DraftOrderStatusType @default(open)
  displayId           Int
  canceledAt          DateTime?
  completedAt         DateTime?
  metadata            Json?
  idempotencyKey      String               @default("")
  noNotificationOrder Boolean              @default(false)
  cart                Cart?                @relation("Cart_draftOrder")
  order               Order?               @relation("DraftOrder_order", fields: [orderId], references: [id])
  orderId             String?              @unique @map("order")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt
}

model Fulfillment {
  id                    String               @id @default(cuid())
  shippedAt             DateTime?
  canceledAt            DateTime?
  data                  Json?
  metadata              Json?
  idempotencyKey        String               @default("")
  noNotification        Boolean              @default(false)
  order                 Order?               @relation("Fulfillment_order", fields: [orderId], references: [id])
  orderId               String?              @map("order")
  claimOrder            ClaimOrder?          @relation("Fulfillment_claimOrder", fields: [claimOrderId], references: [id])
  claimOrderId          String?              @map("claimOrder")
  swap                  Swap?                @relation("Fulfillment_swap", fields: [swapId], references: [id])
  swapId                String?              @map("swap")
  fulfillmentProvider   FulfillmentProvider? @relation("Fulfillment_fulfillmentProvider", fields: [fulfillmentProviderId], references: [id])
  fulfillmentProviderId String?              @map("fulfillmentProvider")
  fulfillmentItems      FulfillmentItem[]    @relation("FulfillmentItem_fulfillment")
  shippingLabels        ShippingLabel[]      @relation("ShippingLabel_fulfillment")
  createdAt             DateTime             @default(now())
  updatedAt             DateTime             @default(now()) @updatedAt

  @@index([orderId])
  @@index([claimOrderId])
  @@index([swapId])
  @@index([fulfillmentProviderId])
}

model FulfillmentItem {
  id            String         @id @default(cuid())
  quantity      Int
  fulfillment   Fulfillment?   @relation("FulfillmentItem_fulfillment", fields: [fulfillmentId], references: [id])
  fulfillmentId String?        @map("fulfillment")
  lineItem      OrderLineItem? @relation("FulfillmentItem_lineItem", fields: [lineItemId], references: [id])
  lineItemId    String?        @map("lineItem")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @default(now()) @updatedAt

  @@index([fulfillmentId])
  @@index([lineItemId])
}

model FulfillmentProvider {
  id                String             @id @default(cuid())
  name              String             @default("")
  code              String             @unique @default("")
  isInstalled       Boolean            @default(true)
  credentials       Json?
  metadata          Json?
  fulfillments      Fulfillment[]      @relation("Fulfillment_fulfillmentProvider")
  regions           Region[]           @relation("FulfillmentProvider_regions")
  shippingOptions   ShippingOption[]   @relation("ShippingOption_fulfillmentProvider")
  shippingProviders ShippingProvider[] @relation("ShippingProvider_fulfillmentProvider")
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @default(now()) @updatedAt
}

model GiftCard {
  id                   String                @id @default(cuid())
  code                 String                @unique @default("")
  value                Int
  balance              Int
  isDisabled           Boolean               @default(false)
  endsAt               DateTime?
  metadata             Json?
  order                Order?                @relation("GiftCard_order", fields: [orderId], references: [id])
  orderId              String?               @map("order")
  carts                Cart[]                @relation("Cart_giftCards")
  giftCardTransactions GiftCardTransaction[] @relation("GiftCardTransaction_giftCard")
  region               Region?               @relation("GiftCard_region", fields: [regionId], references: [id])
  regionId             String?               @map("region")
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  @@index([orderId])
  @@index([regionId])
}

model GiftCardTransaction {
  id         String    @id @default(cuid())
  amount     Int
  isTaxable  Boolean   @default(false)
  taxRate    Float?
  giftCard   GiftCard? @relation("GiftCardTransaction_giftCard", fields: [giftCardId], references: [id])
  giftCardId String?   @map("giftCard")
  order      Order?    @relation("GiftCardTransaction_order", fields: [orderId], references: [id])
  orderId    String?   @map("order")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now()) @updatedAt

  @@index([giftCardId])
  @@index([orderId])
}

model IdempotencyKey {
  id             String    @id @default(cuid())
  idempotencyKey String    @unique @default("")
  requestMethod  String    @default("")
  requestParams  Json?
  requestPath    String    @default("")
  responseCode   Int?
  responseBody   Json?
  recoveryPoint  String    @default("started")
  lockedAt       DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @default(now()) @updatedAt
}

model Invite {
  id        String          @id @default(cuid())
  userEmail String          @default("")
  role      InviteRoleType? @default(member)
  accepted  Boolean         @default(false)
  metadata  Json?
  token     String          @default("")
  expiresAt DateTime        @default(now())
  createdAt DateTime        @default(now())
  updatedAt DateTime        @default(now()) @updatedAt
}

model LineItem {
  id                                  String               @id @default(cuid())
  quantity                            Int
  metadata                            Json?
  isReturn                            Boolean              @default(false)
  isGiftcard                          Boolean              @default(false)
  shouldMerge                         Boolean              @default(true)
  allowDiscounts                      Boolean              @default(true)
  hasShipping                         Boolean              @default(false)
  claimOrder                          ClaimOrder?          @relation("LineItem_claimOrder", fields: [claimOrderId], references: [id])
  claimOrderId                        String?              @map("claimOrder")
  cart                                Cart?                @relation("LineItem_cart", fields: [cartId], references: [id])
  cartId                              String?              @map("cart")
  swap                                Swap?                @relation("LineItem_swap", fields: [swapId], references: [id])
  swapId                              String?              @map("swap")
  productVariant                      ProductVariant?      @relation("LineItem_productVariant", fields: [productVariantId], references: [id])
  productVariantId                    String?              @map("productVariant")
  claimItems                          ClaimItem[]          @relation("ClaimItem_lineItem")
  lineItemAdjustments                 LineItemAdjustment[] @relation("LineItemAdjustment_lineItem")
  lineItemTaxLines                    LineItemTaxLine[]    @relation("LineItemTaxLine_lineItem")
  returnItems                         ReturnItem[]         @relation("ReturnItem_lineItem")
  createdAt                           DateTime             @default(now())
  updatedAt                           DateTime             @default(now()) @updatedAt
  from_OrderLineItem_originalLineItem OrderLineItem[]      @relation("OrderLineItem_originalLineItem")

  @@index([claimOrderId])
  @@index([cartId])
  @@index([swapId])
  @@index([productVariantId])
}

model LineItemAdjustment {
  id          String    @id @default(cuid())
  description String    @default("")
  amount      Int
  metadata    Json?
  discount    Discount? @relation("LineItemAdjustment_discount", fields: [discountId], references: [id])
  discountId  String?   @map("discount")
  lineItem    LineItem? @relation("LineItemAdjustment_lineItem", fields: [lineItemId], references: [id])
  lineItemId  String?   @map("lineItem")
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @default(now()) @updatedAt

  @@index([discountId])
  @@index([lineItemId])
}

model LineItemTaxLine {
  id         String    @id @default(cuid())
  rate       Float
  name       String    @default("")
  code       String    @default("")
  metadata   Json?
  lineItem   LineItem? @relation("LineItemTaxLine_lineItem", fields: [lineItemId], references: [id])
  lineItemId String?   @map("lineItem")
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @default(now()) @updatedAt

  @@index([lineItemId])
}

model Location {
  id          String           @id @default(cuid())
  name        String           @default("")
  description String           @default("")
  address     String           @default("")
  variants    ProductVariant[] @relation("ProductVariant_location")
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @default(now()) @updatedAt
}

model Measurement {
  id               String          @id @default(cuid())
  value            Float
  unit             String          @default("g")
  type             String          @default("weight")
  productVariant   ProductVariant? @relation("Measurement_productVariant", fields: [productVariantId], references: [id])
  productVariantId String?         @map("productVariant")
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @default(now()) @updatedAt

  @@index([productVariantId])
}

model MoneyAmount {
  id               String          @id @default(cuid())
  amount           Int
  compareAmount    Int?
  minQuantity      Int?
  maxQuantity      Int?
  productVariant   ProductVariant? @relation("MoneyAmount_productVariant", fields: [productVariantId], references: [id])
  productVariantId String?         @map("productVariant")
  region           Region?         @relation("MoneyAmount_region", fields: [regionId], references: [id])
  regionId         String?         @map("region")
  currency         Currency?       @relation("MoneyAmount_currency", fields: [currencyId], references: [id])
  currencyId       String?         @map("currency")
  priceList        PriceList?      @relation("MoneyAmount_priceList", fields: [priceListId], references: [id])
  priceListId      String?         @map("priceList")
  priceSet         PriceSet?       @relation("MoneyAmount_priceSet", fields: [priceSetId], references: [id])
  priceSetId       String?         @map("priceSet")
  priceRules       PriceRule[]     @relation("MoneyAmount_priceRules")
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @default(now()) @updatedAt

  @@index([productVariantId])
  @@index([regionId])
  @@index([currencyId])
  @@index([priceListId])
  @@index([priceSetId])
}

model Note {
  id           String   @id @default(cuid())
  value        String   @default("")
  resourceType String   @default("")
  resourceId   String   @default("")
  authorId     String   @default("")
  metadata     Json?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @default(now()) @updatedAt
}

model Notification {
  id                                   String                @id @default(cuid())
  eventName                            String                @default("")
  resourceType                         String                @default("")
  resourceId                           String                @default("")
  to                                   String                @default("")
  data                                 Json?
  parentId                             String                @default("")
  notificationProvider                 NotificationProvider? @relation("Notification_notificationProvider", fields: [notificationProviderId], references: [id])
  notificationProviderId               String?               @map("notificationProvider")
  user                                 User?                 @relation("Notification_user", fields: [userId], references: [id])
  userId                               String?               @map("user")
  otherNotifications                   Notification[]        @relation("Notification_otherNotifications")
  createdAt                            DateTime              @default(now())
  updatedAt                            DateTime              @default(now()) @updatedAt
  from_Notification_otherNotifications Notification[]        @relation("Notification_otherNotifications")

  @@index([notificationProviderId])
  @@index([userId])
}

model NotificationProvider {
  id            String         @id @default(cuid())
  isInstalled   Boolean        @default(true)
  notifications Notification[] @relation("Notification_notificationProvider")
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @default(now()) @updatedAt
}

model OAuth {
  id              String   @id @default(cuid())
  displayName     String   @default("")
  applicationName String   @unique @default("")
  installUrl      String   @default("")
  uninstallUrl    String   @default("")
  data            Json?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @default(now()) @updatedAt
}

model Order {
  id                   String                @id @default(cuid())
  status               OrderStatusType       @default(pending)
  displayId            Int
  email                String                @default("")
  taxRate              Float?
  canceledAt           DateTime?
  metadata             Json?
  idempotencyKey       String                @default("")
  noNotification       Boolean               @default(false)
  externalId           String                @default("")
  shippingAddress      Address?              @relation("Order_shippingAddress", fields: [shippingAddressId], references: [id])
  shippingAddressId    String?               @map("shippingAddress")
  billingAddress       Address?              @relation("Order_billingAddress", fields: [billingAddressId], references: [id])
  billingAddressId     String?               @map("billingAddress")
  currency             Currency?             @relation("Order_currency", fields: [currencyId], references: [id])
  currencyId           String?               @map("currency")
  draftOrder           DraftOrder?           @relation("DraftOrder_order")
  cart                 Cart?                 @relation("Cart_order")
  user                 User?                 @relation("Order_user", fields: [userId], references: [id])
  userId               String?               @map("user")
  region               Region?               @relation("Order_region", fields: [regionId], references: [id])
  regionId             String?               @map("region")
  claimOrders          ClaimOrder[]          @relation("ClaimOrder_order")
  fulfillments         Fulfillment[]         @relation("Fulfillment_order")
  giftCards            GiftCard[]            @relation("GiftCard_order")
  giftCardTransactions GiftCardTransaction[] @relation("GiftCardTransaction_order")
  lineItems            OrderLineItem[]       @relation("OrderLineItem_order")
  discounts            Discount[]            @relation("Discount_orders")
  payments             Payment[]             @relation("Payment_order")
  returns              Return[]              @relation("Return_order")
  shippingMethods      ShippingMethod[]      @relation("ShippingMethod_order")
  swaps                Swap[]                @relation("Swap_order")
  secretKey            String                @default("")
  events               OrderEvent[]          @relation("OrderEvent_order")
  note                 String                @default("")
  shippingLabels       ShippingLabel[]       @relation("ShippingLabel_order")
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @default(now()) @updatedAt

  @@index([shippingAddressId])
  @@index([billingAddressId])
  @@index([currencyId])
  @@index([userId])
  @@index([regionId])
}

model OrderEvent {
  id          String             @id @default(cuid())
  order       Order?             @relation("OrderEvent_order", fields: [orderId], references: [id])
  orderId     String?            @map("order")
  user        User?              @relation("OrderEvent_user", fields: [userId], references: [id])
  userId      String?            @map("user")
  type        OrderEventTypeType @default(STATUS_CHANGE)
  data        Json?              @default("{}")
  time        DateTime?          @default(now())
  createdBy   User?              @relation("OrderEvent_createdBy", fields: [createdById], references: [id])
  createdById String?            @map("createdBy")
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @default(now()) @updatedAt

  @@index([orderId])
  @@index([userId])
  @@index([createdById])
}

model OrderLineItem {
  id                 String            @id @default(cuid())
  quantity           Int
  title              String            @default("")
  sku                String            @default("")
  metadata           Json?
  productData        Json?
  variantData        Json?
  variantTitle       String            @default("")
  formattedUnitPrice String            @default("")
  formattedTotal     String            @default("")
  order              Order?            @relation("OrderLineItem_order", fields: [orderId], references: [id])
  orderId            String?           @map("order")
  productVariant     ProductVariant?   @relation("OrderLineItem_productVariant", fields: [productVariantId], references: [id])
  productVariantId   String?           @map("productVariant")
  moneyAmount        OrderMoneyAmount? @relation("OrderLineItem_moneyAmount", fields: [moneyAmountId], references: [id])
  moneyAmountId      String?           @unique @map("moneyAmount")
  originalLineItem   LineItem?         @relation("OrderLineItem_originalLineItem", fields: [originalLineItemId], references: [id])
  originalLineItemId String?           @map("originalLineItem")
  fulfillmentItems   FulfillmentItem[] @relation("FulfillmentItem_lineItem")
  createdAt          DateTime          @default(now())
  updatedAt          DateTime          @default(now()) @updatedAt

  @@index([orderId])
  @@index([productVariantId])
  @@index([originalLineItemId])
}

model OrderMoneyAmount {
  id             String         @id @default(cuid())
  amount         Int
  originalAmount Int
  priceData      Json?
  metadata       Json?
  orderLineItem  OrderLineItem? @relation("OrderLineItem_moneyAmount")
  currency       Currency?      @relation("OrderMoneyAmount_currency", fields: [currencyId], references: [id])
  currencyId     String?        @map("currency")
  region         Region?        @relation("OrderMoneyAmount_region", fields: [regionId], references: [id])
  regionId       String?        @map("region")
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @default(now()) @updatedAt

  @@index([currencyId])
  @@index([regionId])
}

model Payment {
  id                  String             @id @default(cuid())
  status              PaymentStatusType  @default(pending)
  amount              Int
  currencyCode        String             @default("")
  amountRefunded      Int                @default(0)
  data                Json?
  capturedAt          DateTime?
  canceledAt          DateTime?
  metadata            Json?
  idempotencyKey      String             @default("")
  cart                Cart?              @relation("Cart_payment")
  paymentCollection   PaymentCollection? @relation("Payment_paymentCollection", fields: [paymentCollectionId], references: [id])
  paymentCollectionId String?            @map("paymentCollection")
  swap                Swap?              @relation("Payment_swap", fields: [swapId], references: [id])
  swapId              String?            @unique @map("swap")
  currency            Currency?          @relation("Payment_currency", fields: [currencyId], references: [id])
  currencyId          String?            @map("currency")
  order               Order?             @relation("Payment_order", fields: [orderId], references: [id])
  orderId             String?            @map("order")
  captures            Capture[]          @relation("Capture_payment")
  refunds             Refund[]           @relation("Refund_payment")
  user                User?              @relation("Payment_user", fields: [userId], references: [id])
  userId              String?            @map("user")
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @default(now()) @updatedAt

  @@index([paymentCollectionId])
  @@index([currencyId])
  @@index([orderId])
  @@index([userId])
}

model PaymentCollection {
  id               String                            @id @default(cuid())
  description      PaymentCollectionDescriptionType? @default(default)
  amount           Int
  authorizedAmount Int?                              @default(0)
  refundedAmount   Int?                              @default(0)
  metadata         Json?
  paymentSessions  PaymentSession[]                  @relation("PaymentSession_paymentCollection")
  payments         Payment[]                         @relation("Payment_paymentCollection")
  cart             Cart?                             @relation("Cart_paymentCollection")
  createdAt        DateTime                          @default(now())
  updatedAt        DateTime                          @default(now()) @updatedAt
}

model PaymentProvider {
  id                          String           @id @default(cuid())
  name                        String           @default("")
  code                        String           @unique @default("")
  isInstalled                 Boolean          @default(true)
  credentials                 Json?            @default("{}")
  metadata                    Json?            @default("{}")
  createPaymentFunction       String           @default("")
  capturePaymentFunction      String           @default("")
  refundPaymentFunction       String           @default("")
  getPaymentStatusFunction    String           @default("")
  generatePaymentLinkFunction String           @default("")
  handleWebhookFunction       String           @default("")
  regions                     Region[]         @relation("PaymentProvider_regions")
  sessions                    PaymentSession[] @relation("PaymentSession_paymentProvider")
  createdAt                   DateTime         @default(now())
  updatedAt                   DateTime         @default(now()) @updatedAt
}

model PaymentSession {
  id                  String             @id @default(cuid())
  isSelected          Boolean            @default(false)
  isInitiated         Boolean            @default(false)
  amount              Int
  data                Json?              @default("{}")
  idempotencyKey      String             @default("")
  paymentCollection   PaymentCollection? @relation("PaymentSession_paymentCollection", fields: [paymentCollectionId], references: [id])
  paymentCollectionId String?            @map("paymentCollection")
  paymentProvider     PaymentProvider?   @relation("PaymentSession_paymentProvider", fields: [paymentProviderId], references: [id])
  paymentProviderId   String?            @map("paymentProvider")
  paymentAuthorizedAt DateTime?
  createdAt           DateTime           @default(now())
  updatedAt           DateTime           @default(now()) @updatedAt

  @@index([idempotencyKey])
  @@index([paymentCollectionId])
  @@index([paymentProviderId])
}

model PriceList {
  id             String              @id @default(cuid())
  name           String              @default("")
  description    String              @default("")
  type           PriceListTypeType   @default(sale)
  status         PriceListStatusType @default(draft)
  startsAt       DateTime?
  endsAt         DateTime?
  moneyAmounts   MoneyAmount[]       @relation("MoneyAmount_priceList")
  customerGroups CustomerGroup[]     @relation("CustomerGroup_priceLists")
  createdAt      DateTime            @default(now())
  updatedAt      DateTime            @default(now()) @updatedAt
}

model PriceRule {
  id            String            @id @default(cuid())
  type          PriceRuleTypeType
  value         Float
  priority      Int?              @default(0)
  ruleAttribute String            @default("")
  ruleValue     String            @default("")
  moneyAmounts  MoneyAmount[]     @relation("MoneyAmount_priceRules")
  priceSet      PriceSet?         @relation("PriceRule_priceSet", fields: [priceSetId], references: [id])
  priceSetId    String?           @map("priceSet")
  createdAt     DateTime          @default(now())
  updatedAt     DateTime          @default(now()) @updatedAt

  @@index([priceSetId])
}

model PriceSet {
  id         String        @id @default(cuid())
  prices     MoneyAmount[] @relation("MoneyAmount_priceSet")
  priceRules PriceRule[]   @relation("PriceRule_priceSet")
  ruleTypes  RuleType[]    @relation("PriceSet_ruleTypes")
  createdAt  DateTime      @default(now())
  updatedAt  DateTime      @default(now()) @updatedAt
}

model Product {
  id                 String              @id @default(cuid())
  title              String              @default("")
  description        Json                @default("[{\"type\":\"paragraph\",\"children\":[{\"text\":\"\"}]}]")
  handle             String              @unique @default("")
  subtitle           String              @default("")
  isGiftcard         Boolean             @default(false)
  metadata           Json?
  discountable       Boolean             @default(true)
  status             ProductStatusType   @default(draft)
  externalId         String              @default("")
  productCollections ProductCollection[] @relation("Product_productCollections")
  productCategories  ProductCategory[]   @relation("Product_productCategories")
  shippingProfile    ShippingProfile?    @relation("Product_shippingProfile", fields: [shippingProfileId], references: [id])
  shippingProfileId  String?             @map("shippingProfile")
  productType        ProductType?        @relation("Product_productType", fields: [productTypeId], references: [id])
  productTypeId      String?             @map("productType")
  discountConditions DiscountCondition[] @relation("DiscountCondition_products")
  discountRules      DiscountRule[]      @relation("DiscountRule_products")
  productImages      ProductImage[]      @relation("Product_productImages")
  productOptions     ProductOption[]     @relation("ProductOption_product")
  productTags        ProductTag[]        @relation("Product_productTags")
  taxRates           TaxRate[]           @relation("Product_taxRates")
  productVariants    ProductVariant[]    @relation("ProductVariant_product")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt

  @@index([shippingProfileId])
  @@index([productTypeId])
}

model ProductCategory {
  id                 String              @id @default(cuid())
  title              String              @default("")
  handle             String              @unique @default("")
  metadata           Json?
  isInternal         Boolean             @default(false)
  isActive           Boolean             @default(true)
  discountConditions DiscountCondition[] @relation("DiscountCondition_productCategories")
  products           Product[]           @relation("Product_productCategories")
  parentCategory     ProductCategory?    @relation("ProductCategory_parentCategory", fields: [parentCategoryId], references: [id])
  parentCategoryId   String?             @map("parentCategory")
  categoryChildren   ProductCategory[]   @relation("ProductCategory_parentCategory")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt

  @@index([parentCategoryId])
}

model ProductCollection {
  id                 String              @id @default(cuid())
  title              String              @default("")
  handle             String              @unique @default("")
  metadata           Json?
  discountConditions DiscountCondition[] @relation("DiscountCondition_productCollections")
  products           Product[]           @relation("Product_productCollections")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt
}

model ProductImage {
  id              String    @id @default(cuid())
  image_id        String?
  image_filesize  Int?
  image_width     Int?
  image_height    Int?
  image_extension String?
  imagePath       String    @default("")
  altText         String    @default("")
  products        Product[] @relation("Product_productImages")
  metadata        Json?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @default(now()) @updatedAt
}

model ProductOption {
  id                  String               @id @default(cuid())
  title               String               @default("")
  metadata            Json?
  product             Product?             @relation("ProductOption_product", fields: [productId], references: [id])
  productId           String?              @map("product")
  productOptionValues ProductOptionValue[] @relation("ProductOptionValue_productOption")
  createdAt           DateTime             @default(now())
  updatedAt           DateTime             @default(now()) @updatedAt

  @@index([productId])
}

model ProductOptionValue {
  id              String           @id @default(cuid())
  value           String           @default("")
  metadata        Json?
  productVariants ProductVariant[] @relation("ProductOptionValue_productVariants")
  productOption   ProductOption?   @relation("ProductOptionValue_productOption", fields: [productOptionId], references: [id])
  productOptionId String?          @map("productOption")
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now()) @updatedAt

  @@index([productOptionId])
}

model ProductTag {
  id                 String              @id @default(cuid())
  value              String              @default("")
  metadata           Json?
  discountConditions DiscountCondition[] @relation("DiscountCondition_productTags")
  products           Product[]           @relation("Product_productTags")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt
}

model ProductType {
  id                 String              @id @default(cuid())
  value              String              @default("")
  metadata           Json?
  discountConditions DiscountCondition[] @relation("DiscountCondition_productTypes")
  products           Product[]           @relation("Product_productType")
  taxRates           TaxRate[]           @relation("ProductType_taxRates")
  createdAt          DateTime            @default(now())
  updatedAt          DateTime            @default(now()) @updatedAt
}

model ProductVariant {
  id                                String               @id @default(cuid())
  title                             String               @default("")
  sku                               String               @default("")
  barcode                           String               @default("")
  ean                               String               @default("")
  upc                               String               @default("")
  inventoryQuantity                 Int
  allowBackorder                    Boolean              @default(false)
  manageInventory                   Boolean              @default(true)
  hsCode                            String               @default("")
  originCountry                     String               @default("")
  midCode                           String               @default("")
  material                          String               @default("")
  metadata                          Json?
  variantRank                       Int?                 @default(0)
  product                           Product?             @relation("ProductVariant_product", fields: [productId], references: [id])
  productId                         String?              @map("product")
  claimItems                        ClaimItem[]          @relation("ClaimItem_productVariant")
  lineItems                         LineItem[]           @relation("LineItem_productVariant")
  prices                            MoneyAmount[]        @relation("MoneyAmount_productVariant")
  productOptionValues               ProductOptionValue[] @relation("ProductOptionValue_productVariants")
  location                          Location?            @relation("ProductVariant_location", fields: [locationId], references: [id])
  locationId                        String?              @map("location")
  stockMovements                    StockMovement[]      @relation("StockMovement_variant")
  measurements                      Measurement[]        @relation("Measurement_productVariant")
  createdAt                         DateTime             @default(now())
  updatedAt                         DateTime             @default(now()) @updatedAt
  from_OrderLineItem_productVariant OrderLineItem[]      @relation("OrderLineItem_productVariant")

  @@index([productId])
  @@index([locationId])
}

model Refund {
  id             String           @id @default(cuid())
  amount         Int
  note           String           @default("")
  reason         RefundReasonType
  metadata       Json?
  idempotencyKey String           @default("")
  payment        Payment?         @relation("Refund_payment", fields: [paymentId], references: [id])
  paymentId      String?          @map("payment")
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @default(now()) @updatedAt

  @@index([paymentId])
}

model Region {
  id                           String                @id @default(cuid())
  code                         String                @unique @default("")
  name                         String                @default("")
  taxRate                      Float
  taxCode                      String                @default("")
  metadata                     Json?
  giftCardsTaxable             Boolean               @default(true)
  automaticTaxes               Boolean               @default(true)
  currency                     Currency?             @relation("Region_currency", fields: [currencyId], references: [id])
  currencyId                   String?               @map("currency")
  carts                        Cart[]                @relation("Cart_region")
  countries                    Country[]             @relation("Country_region")
  discounts                    Discount[]            @relation("Discount_regions")
  giftCards                    GiftCard[]            @relation("GiftCard_region")
  moneyAmounts                 MoneyAmount[]         @relation("MoneyAmount_region")
  orders                       Order[]               @relation("Order_region")
  taxProvider                  TaxProvider?          @relation("Region_taxProvider", fields: [taxProviderId], references: [id])
  taxProviderId                String?               @map("taxProvider")
  fulfillmentProviders         FulfillmentProvider[] @relation("FulfillmentProvider_regions")
  paymentProviders             PaymentProvider[]     @relation("PaymentProvider_regions")
  shippingOptions              ShippingOption[]      @relation("ShippingOption_region")
  taxRates                     TaxRate[]             @relation("TaxRate_region")
  shippingProviders            ShippingProvider[]    @relation("Region_shippingProviders")
  createdAt                    DateTime              @default(now())
  updatedAt                    DateTime              @default(now()) @updatedAt
  from_OrderMoneyAmount_region OrderMoneyAmount[]    @relation("OrderMoneyAmount_region")

  @@index([currencyId])
  @@index([taxProviderId])
}

model Return {
  id               String           @id @default(cuid())
  status           ReturnStatusType @default(requested)
  shippingData     Json?
  refundAmount     Int
  receivedAt       DateTime?
  metadata         Json?
  idempotencyKey   String           @default("")
  noNotification   Boolean          @default(false)
  claimOrder       ClaimOrder?      @relation("ClaimOrder_return")
  swap             Swap?            @relation("Return_swap", fields: [swapId], references: [id])
  swapId           String?          @unique @map("swap")
  order            Order?           @relation("Return_order", fields: [orderId], references: [id])
  orderId          String?          @map("order")
  returnItems      ReturnItem[]     @relation("ReturnItem_return")
  shippingMethod   ShippingMethod?  @relation("Return_shippingMethod", fields: [shippingMethodId], references: [id])
  shippingMethodId String?          @unique @map("shippingMethod")
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @default(now()) @updatedAt

  @@index([orderId])
}

model ReturnItem {
  id                String        @id @default(cuid())
  quantity          Int
  isRequested       Boolean       @default(true)
  requestedQuantity Int?
  receivedQuantity  Int?
  metadata          Json?
  note              String        @default("")
  return            Return?       @relation("ReturnItem_return", fields: [returnId], references: [id])
  returnId          String?       @map("return")
  lineItem          LineItem?     @relation("ReturnItem_lineItem", fields: [lineItemId], references: [id])
  lineItemId        String?       @map("lineItem")
  returnReason      ReturnReason? @relation("ReturnItem_returnReason", fields: [returnReasonId], references: [id])
  returnReasonId    String?       @map("returnReason")
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @default(now()) @updatedAt

  @@index([returnId])
  @@index([lineItemId])
  @@index([returnReasonId])
}

model ReturnReason {
  id                                   String         @id @default(cuid())
  value                                String         @unique @default("")
  label                                String         @default("")
  description                          String         @default("")
  metadata                             Json?
  parentReturnReason                   ReturnReason?  @relation("ReturnReason_parentReturnReason", fields: [parentReturnReasonId], references: [id])
  parentReturnReasonId                 String?        @map("parentReturnReason")
  returnItems                          ReturnItem[]   @relation("ReturnItem_returnReason")
  createdAt                            DateTime       @default(now())
  updatedAt                            DateTime       @default(now()) @updatedAt
  from_ReturnReason_parentReturnReason ReturnReason[] @relation("ReturnReason_parentReturnReason")

  @@index([parentReturnReasonId])
}

model Role {
  id                       String   @id @default(cuid())
  name                     String   @default("")
  canAccessDashboard       Boolean  @default(false)
  canReadOrders            Boolean  @default(false)
  canManageOrders          Boolean  @default(false)
  canReadProducts          Boolean  @default(false)
  canManageProducts        Boolean  @default(false)
  canReadFulfillments      Boolean  @default(false)
  canManageFulfillments    Boolean  @default(false)
  canReadUsers             Boolean  @default(false)
  canManageUsers           Boolean  @default(false)
  canReadRoles             Boolean  @default(false)
  canManageRoles           Boolean  @default(false)
  canReadCheckouts         Boolean  @default(false)
  canManageCheckouts       Boolean  @default(false)
  canReadDiscounts         Boolean  @default(false)
  canManageDiscounts       Boolean  @default(false)
  canReadGiftCards         Boolean  @default(false)
  canManageGiftCards       Boolean  @default(false)
  canReadReturns           Boolean  @default(false)
  canManageReturns         Boolean  @default(false)
  canReadSalesChannels     Boolean  @default(false)
  canManageSalesChannels   Boolean  @default(false)
  canReadPayments          Boolean  @default(false)
  canManagePayments        Boolean  @default(false)
  canReadIdempotencyKeys   Boolean  @default(false)
  canManageIdempotencyKeys Boolean  @default(false)
  canReadApps              Boolean  @default(false)
  canManageApps            Boolean  @default(false)
  canManageKeys            Boolean  @default(false)
  canManageOnboarding      Boolean  @default(false)
  assignedTo               User[]   @relation("User_role")
  createdAt                DateTime @default(now())
  updatedAt                DateTime @default(now()) @updatedAt
}

model RuleType {
  id            String     @id @default(cuid())
  name          String     @default("")
  ruleAttribute String     @unique @default("")
  priceSets     PriceSet[] @relation("PriceSet_ruleTypes")
  createdAt     DateTime   @default(now())
  updatedAt     DateTime   @default(now()) @updatedAt
}

model SalesChannel {
  id          String   @id @default(cuid())
  name        String   @default("")
  description String   @default("")
  isDisabled  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
}

model ShippingLabel {
  id             String                  @id @default(cuid())
  status         ShippingLabelStatusType @default(created)
  labelUrl       String                  @default("")
  carrier        String                  @default("")
  service        String                  @default("")
  rate           Json?
  trackingNumber String                  @default("")
  trackingUrl    String                  @default("")
  order          Order?                  @relation("ShippingLabel_order", fields: [orderId], references: [id])
  orderId        String?                 @map("order")
  provider       ShippingProvider?       @relation("ShippingLabel_provider", fields: [providerId], references: [id])
  providerId     String?                 @map("provider")
  fulfillment    Fulfillment?            @relation("ShippingLabel_fulfillment", fields: [fulfillmentId], references: [id])
  fulfillmentId  String?                 @map("fulfillment")
  data           Json?
  metadata       Json?
  createdAt      DateTime                @default(now())
  updatedAt      DateTime                @default(now()) @updatedAt

  @@index([orderId])
  @@index([providerId])
  @@index([fulfillmentId])
}

model ShippingMethod {
  id                     String                  @id @default(cuid())
  price                  Int
  data                   Json?
  return                 Return?                 @relation("Return_shippingMethod")
  order                  Order?                  @relation("ShippingMethod_order", fields: [orderId], references: [id])
  orderId                String?                 @map("order")
  claimOrder             ClaimOrder?             @relation("ShippingMethod_claimOrder", fields: [claimOrderId], references: [id])
  claimOrderId           String?                 @map("claimOrder")
  cart                   Cart?                   @relation("ShippingMethod_cart", fields: [cartId], references: [id])
  cartId                 String?                 @map("cart")
  swap                   Swap?                   @relation("ShippingMethod_swap", fields: [swapId], references: [id])
  swapId                 String?                 @map("swap")
  shippingOption         ShippingOption?         @relation("ShippingMethod_shippingOption", fields: [shippingOptionId], references: [id])
  shippingOptionId       String?                 @map("shippingOption")
  shippingMethodTaxLines ShippingMethodTaxLine[] @relation("ShippingMethodTaxLine_shippingMethod")
  createdAt              DateTime                @default(now())
  updatedAt              DateTime                @default(now()) @updatedAt

  @@index([orderId])
  @@index([claimOrderId])
  @@index([cartId])
  @@index([swapId])
  @@index([shippingOptionId])
}

model ShippingMethodTaxLine {
  id               String          @id @default(cuid())
  rate             Float
  name             String          @default("")
  code             String          @default("")
  metadata         Json?
  shippingMethod   ShippingMethod? @relation("ShippingMethodTaxLine_shippingMethod", fields: [shippingMethodId], references: [id])
  shippingMethodId String?         @map("shippingMethod")
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @default(now()) @updatedAt

  @@index([shippingMethodId])
}

model ShippingOption {
  id                         String                      @id @default(cuid())
  name                       String                      @default("")
  uniqueKey                  String                      @unique @default("")
  priceType                  ShippingOptionPriceTypeType
  amount                     Int?
  isReturn                   Boolean                     @default(false)
  data                       Json?
  metadata                   Json?
  adminOnly                  Boolean                     @default(false)
  region                     Region?                     @relation("ShippingOption_region", fields: [regionId], references: [id])
  regionId                   String?                     @map("region")
  fulfillmentProvider        FulfillmentProvider?        @relation("ShippingOption_fulfillmentProvider", fields: [fulfillmentProviderId], references: [id])
  fulfillmentProviderId      String?                     @map("fulfillmentProvider")
  shippingProfile            ShippingProfile?            @relation("ShippingOption_shippingProfile", fields: [shippingProfileId], references: [id])
  shippingProfileId          String?                     @map("shippingProfile")
  customShippingOptions      CustomShippingOption[]      @relation("CustomShippingOption_shippingOption")
  shippingMethods            ShippingMethod[]            @relation("ShippingMethod_shippingOption")
  shippingOptionRequirements ShippingOptionRequirement[] @relation("ShippingOptionRequirement_shippingOption")
  taxRates                   TaxRate[]                   @relation("ShippingOption_taxRates")
  createdAt                  DateTime                    @default(now())
  updatedAt                  DateTime                    @default(now()) @updatedAt

  @@index([regionId])
  @@index([fulfillmentProviderId])
  @@index([shippingProfileId])
}

model ShippingOptionRequirement {
  id               String                            @id @default(cuid())
  type             ShippingOptionRequirementTypeType
  amount           Int
  shippingOption   ShippingOption?                   @relation("ShippingOptionRequirement_shippingOption", fields: [shippingOptionId], references: [id])
  shippingOptionId String?                           @map("shippingOption")
  createdAt        DateTime                          @default(now())
  updatedAt        DateTime                          @default(now()) @updatedAt

  @@index([shippingOptionId])
}

model ShippingProfile {
  id              String                  @id @default(cuid())
  name            String                  @default("")
  type            ShippingProfileTypeType
  metadata        Json?
  products        Product[]               @relation("Product_shippingProfile")
  shippingOptions ShippingOption[]        @relation("ShippingOption_shippingProfile")
  createdAt       DateTime                @default(now())
  updatedAt       DateTime                @default(now()) @updatedAt
}

model ShippingProvider {
  id                      String               @id @default(cuid())
  name                    String               @default("")
  isActive                Boolean              @default(false)
  accessToken             String               @default("")
  createLabelFunction     String               @default("")
  getRatesFunction        String               @default("")
  validateAddressFunction String               @default("")
  trackShipmentFunction   String               @default("")
  cancelLabelFunction     String               @default("")
  metadata                Json?
  regions                 Region[]             @relation("Region_shippingProviders")
  labels                  ShippingLabel[]      @relation("ShippingLabel_provider")
  fulfillmentProvider     FulfillmentProvider? @relation("ShippingProvider_fulfillmentProvider", fields: [fulfillmentProviderId], references: [id])
  fulfillmentProviderId   String?              @map("fulfillmentProvider")
  fromAddress             Address?             @relation("ShippingProvider_fromAddress", fields: [fromAddressId], references: [id])
  fromAddressId           String?              @map("fromAddress")
  createdAt               DateTime             @default(now())
  updatedAt               DateTime             @default(now()) @updatedAt

  @@index([fulfillmentProviderId])
  @@index([fromAddressId])
}

model StockMovement {
  id        String                @id @default(cuid())
  type      StockMovementTypeType
  quantity  Int
  reason    String                @default("")
  note      String                @default("")
  variant   ProductVariant?       @relation("StockMovement_variant", fields: [variantId], references: [id])
  variantId String?               @map("variant")
  createdAt DateTime              @default(now())
  updatedAt DateTime              @default(now()) @updatedAt

  @@index([variantId])
}

model Store {
  id                  String     @id @default(cuid())
  name                String     @default("Openfront Store")
  defaultCurrencyCode String     @default("usd")
  metadata            Json?
  swapLinkTemplate    String     @default("")
  paymentLinkTemplate String     @default("")
  inviteLinkTemplate  String     @default("")
  currencies          Currency[] @relation("Currency_stores")
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @default(now()) @updatedAt
}

model Swap {
  id                String                    @id @default(cuid())
  fulfillmentStatus SwapFulfillmentStatusType
  paymentStatus     SwapPaymentStatusType
  differenceDue     Int?
  confirmedAt       DateTime?
  metadata          Json?
  idempotencyKey    String                    @default("")
  noNotification    Boolean                   @default(false)
  canceledAt        DateTime?
  allowBackorder    Boolean                   @default(false)
  cart              Cart?                     @relation("Cart_swap")
  order             Order?                    @relation("Swap_order", fields: [orderId], references: [id])
  orderId           String?                   @map("order")
  address           Address?                  @relation("Swap_address", fields: [addressId], references: [id])
  addressId         String?                   @map("address")
  lineItems         LineItem[]                @relation("LineItem_swap")
  fulfillments      Fulfillment[]             @relation("Fulfillment_swap")
  payment           Payment?                  @relation("Payment_swap")
  return            Return?                   @relation("Return_swap")
  shippingMethods   ShippingMethod[]          @relation("ShippingMethod_swap")
  createdAt         DateTime                  @default(now())
  updatedAt         DateTime                  @default(now()) @updatedAt

  @@index([orderId])
  @@index([addressId])
}

model TaxProvider {
  id          String   @id @default(cuid())
  isInstalled Boolean  @default(true)
  regions     Region[] @relation("Region_taxProvider")
}

model TaxRate {
  id              String           @id @default(cuid())
  rate            Float?
  code            String           @default("")
  name            String           @default("")
  metadata        Json?
  products        Product[]        @relation("Product_taxRates")
  productTypes    ProductType[]    @relation("ProductType_taxRates")
  region          Region?          @relation("TaxRate_region", fields: [regionId], references: [id])
  regionId        String?          @map("region")
  shippingOptions ShippingOption[] @relation("ShippingOption_taxRates")
  createdAt       DateTime         @default(now())
  updatedAt       DateTime         @default(now()) @updatedAt

  @@index([regionId])
}

model Team {
  id          String   @id @default(cuid())
  name        String   @default("")
  description String   @default("")
  members     User[]   @relation("User_team")
  leader      User?    @relation("Team_leader", fields: [leaderId], references: [id])
  leaderId    String?  @map("leader")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt

  @@index([leaderId])
}

model User {
  id                        String          @id @default(cuid())
  name                      String          @default("")
  email                     String          @unique @default("")
  password                  String
  role                      Role?           @relation("User_role", fields: [roleId], references: [id])
  roleId                    String?         @map("role")
  apiKeys                   ApiKey[]        @relation("ApiKey_user")
  phone                     String          @default("")
  hasAccount                Boolean         @default(false)
  addresses                 Address[]       @relation("Address_user")
  orders                    Order[]         @relation("Order_user")
  orderEvents               OrderEvent[]    @relation("OrderEvent_user")
  carts                     Cart[]          @relation("Cart_user")
  customerGroups            CustomerGroup[] @relation("CustomerGroup_users")
  notifications             Notification[]  @relation("Notification_user")
  payments                  Payment[]       @relation("Payment_user")
  batchJobs                 BatchJob[]      @relation("BatchJob_createdBy")
  team                      Team?           @relation("User_team", fields: [teamId], references: [id])
  teamId                    String?         @map("team")
  teamLead                  Team[]          @relation("Team_leader")
  userField                 UserField?      @relation("User_userField", fields: [userFieldId], references: [id])
  userFieldId               String?         @unique @map("userField")
  onboardingStatus          String?         @default("not_started")
  createdAt                 DateTime        @default(now())
  updatedAt                 DateTime        @default(now()) @updatedAt
  passwordResetToken        String?
  passwordResetIssuedAt     DateTime?
  passwordResetRedeemedAt   DateTime?
  from_OrderEvent_createdBy OrderEvent[]    @relation("OrderEvent_createdBy")

  @@index([roleId])
  @@index([teamId])
}

model UserField {
  id                  String    @id @default(cuid())
  user                User?     @relation("User_userField")
  lastLoginIp         String    @default("")
  lastLoginUserAgent  String    @default("")
  loginHistory        Json?     @default("[]")
  preferences         Json?     @default("{\"theme\":\"light\",\"notifications\":true,\"emailNotifications\":true}")
  notes               String    @default("")
  lastPasswordChange  DateTime?
  failedLoginAttempts Json?     @default("{\"count\":0,\"lastAttempt\":null,\"lockedUntil\":null}")
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @default(now()) @updatedAt
}

enum BatchJobTypeType {
  PRODUCT_IMPORT
  ORDER_EXPORT
  INVENTORY_UPDATE
  PRICE_UPDATE
}

enum BatchJobStatusType {
  CREATED
  PROCESSING
  COMPLETED
  FAILED
  CANCELED
}

enum CartTypeType {
  default
  swap
  draft_order
  payment_link
  claim
}

enum ClaimItemReasonType {
  missing_item
  wrong_item
  production_failure
  other
}

enum ClaimOrderPaymentStatusType {
  na
  not_refunded
  refunded
}

enum ClaimOrderFulfillmentStatusType {
  not_fulfilled
  partially_fulfilled
  fulfilled
  partially_shipped
  shipped
  partially_returned
  returned
  canceled
  requires_action
}

enum ClaimOrderTypeType {
  refund
  replace
}

enum DiscountConditionTypeType {
  products
  product_types
  product_collections
  product_tags
  customer_groups
}

enum DiscountConditionOperatorType {
  in
  not_in
}

enum DiscountRuleTypeType {
  fixed
  percentage
  free_shipping
}

enum DiscountRuleAllocationType {
  total
  item
}

enum DraftOrderStatusType {
  open
  completed
}

enum InviteRoleType {
  admin
  member
  developer
}

enum OrderStatusType {
  pending
  completed
  archived
  canceled
  requires_action
}

enum OrderEventTypeType {
  ORDER_PLACED
  STATUS_CHANGE
  PAYMENT_STATUS_CHANGE
  PAYMENT_CAPTURED
  FULFILLMENT_STATUS_CHANGE
  NOTE_ADDED
  EMAIL_SENT
  TRACKING_NUMBER_ADDED
  RETURN_REQUESTED
  REFUND_PROCESSED
}

enum PaymentStatusType {
  pending
  authorized
  captured
  failed
  canceled
}

enum PaymentCollectionDescriptionType {
  default
  refund
}

enum PriceListTypeType {
  sale
  override
}

enum PriceListStatusType {
  active
  draft
}

enum PriceRuleTypeType {
  fixed
  percentage
}

enum ProductStatusType {
  draft
  proposed
  published
  rejected
}

enum RefundReasonType {
  discount
  return
  swap
  claim
  other
}

enum ReturnStatusType {
  requested
  received
  requires_action
  canceled
}

enum ShippingLabelStatusType {
  created
  purchased
  failed
}

enum ShippingOptionPriceTypeType {
  flat_rate
  calculated
  free
}

enum ShippingOptionRequirementTypeType {
  min_subtotal
  max_subtotal
}

enum ShippingProfileTypeType {
  default
  gift_card
  custom
}

enum StockMovementTypeType {
  RECEIVE
  REMOVE
}

enum SwapFulfillmentStatusType {
  not_fulfilled
  fulfilled
  shipped
  partially_shipped
  canceled
  requires_action
}

enum SwapPaymentStatusType {
  not_paid
  awaiting
  captured
  confirmed
  canceled
  difference_refunded
  partially_refunded
  refunded
  requires_action
}
