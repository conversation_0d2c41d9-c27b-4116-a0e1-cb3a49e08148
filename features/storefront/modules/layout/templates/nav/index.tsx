import { Suspense } from 'react';
import { Triangle, Circle, Square } from 'lucide-react';

import { listRegions } from '@/features/storefront/lib/data/regions';

import LocalizedClientLink from '@/features/storefront/modules/common/components/localized-client-link';
import CartButton from '@/features/storefront/modules/layout/components/cart-button';
import SideMenu from '@/features/storefront/modules/layout/components/side-menu';
import { Tektur } from 'next/font/google';

const tektur = Tektur({
  subsets: ['latin'],
  display: 'swap',
  adjustFontFallback: false,
});

import { cn } from '@/lib/utils/cn';

export default async function Nav() {
  const { regions } = await listRegions();

  return (
    <div className="sticky top-0 inset-x-0 z-50 group">
      <header className="relative h-16 mx-auto border-b duration-200 bg-background border-border">
        <nav className="max-w-[1440px] mx-auto px-6 text-muted-foreground flex items-center justify-between w-full h-full text-xs leading-5 font-normal">
          <div className="flex-1 basis-0 h-full flex items-center">
            <div className="h-full">
              <SideMenu regions={regions} />
            </div>
          </div>

          <div className="flex items-center h-full">
            <LocalizedClientLink
              href="/"
              className={cn(
                tektur.className,
                'flex items-center gap-2 text-2xl text-foreground hover:text-muted-foreground opacity-75'
              )}
              data-testid="nav-store-link"
            >
              {/* <div className="relative h-7.5 w-7.5 mt-1">
                <Triangle className="absolute left-1 top-1 w-2.5 h-2.5 fill-indigo-200 stroke-indigo-400 dark:stroke-indigo-600 dark:fill-indigo-950 rotate-[90deg]" />
                <Square className="absolute right-[.2rem] top-1 w-2.5 h-2.5 fill-orange-300 stroke-orange-500 dark:stroke-amber-600 dark:fill-amber-950 rotate-[40deg]" />
                <Circle className="absolute bottom-2 left-1/2 -translate-x-1/2 w-2.5 h-2.5 fill-emerald-200 stroke-emerald-400 dark:stroke-emerald-600 dark:fill-emerald-900" />
              </div> */}
              <svg
                width="16"
                height="16"
                viewBox="0 0 200 200"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g clipPath="url(#clip0_238_1296)">
                  <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M100 0H0L100 100H0L100 200H200L100 100H200L100 0Z"
                    fill="url(#paint0_linear_238_1296)"
                  />
                </g>
                <defs>
                  <linearGradient
                    id="paint0_linear_238_1296"
                    x1="20.5"
                    y1="16"
                    x2="100"
                    y2="200"
                    gradientUnits="userSpaceOnUse"
                  >
                    <stop stopColor="#2b7fff" />
                    <stop offset="1" stopColor="#4f39f6" />
                  </linearGradient>
                  <clipPath id="clip0_238_1296">
                    <rect width="200" height="200" fill="white" />
                  </clipPath>
                </defs>
              </svg>
              <h1 className="flex items-center tracking-wide text-xl">
                <span className="font-medium">impossible</span>
                <span className="mx-1.5 text-base text-muted-foreground">
                  x
                </span>
                <span className="font-light">tees</span>
              </h1>
            </LocalizedClientLink>
          </div>

          <div className="flex items-center gap-x-6 h-full flex-1 basis-0 justify-end">
            <div className="hidden lg:flex items-center gap-x-6 h-full">
              <LocalizedClientLink
                className="hover:text-foreground"
                href="/account"
                data-testid="nav-account-link"
              >
                Account
              </LocalizedClientLink>
            </div>
            <Suspense
              fallback={
                <LocalizedClientLink
                  className="hover:text-foreground flex gap-2"
                  href="/cart"
                  data-testid="nav-cart-link"
                >
                  Cart (0)
                </LocalizedClientLink>
              }
            >
              <CartButton />
            </Suspense>
          </div>
        </nav>
      </header>
    </div>
  );
}
