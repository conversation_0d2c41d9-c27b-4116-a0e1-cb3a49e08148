'use server';

import { keystoneClient } from '@/features/dashboard/lib/keystoneClient';
import { gql } from 'graphql-request';

// Types for Price List data
export interface PriceListCustomerGroup {
  id: string;
  name: string;
  usersCount?: number;
}

export interface PriceListMoneyAmount {
  id: string;
  amount: number;
  currencyCode: string;
  productVariant?: {
    id: string;
    title: string;
    product: {
      id: string;
      title: string;
    };
  };
}

export interface PriceListWithRelations {
  id: string;
  name: string;
  description: string;
  type: 'sale' | 'override';
  status: 'active' | 'draft';
  startsAt?: string;
  endsAt?: string;
  customerGroups: PriceListCustomerGroup[];
  moneyAmounts: PriceListMoneyAmount[];
  moneyAmountsCount: number;
  createdAt: string;
  updatedAt: string;
}

// GraphQL Queries
const GET_FILTERED_PRICE_LISTS = gql`
  query GetFilteredPriceLists(
    $where: PriceListWhereInput
    $orderBy: [PriceListOrderByInput!]
    $take: Int
    $skip: Int
  ) {
    priceLists(where: $where, orderBy: $orderBy, take: $take, skip: $skip) {
      id
      name
      description
      type
      status
      startsAt
      endsAt
      customerGroups {
        id
        name
        usersCount
      }
      moneyAmounts(take: 5) {
        id
        amount
        currencyCode
        productVariant {
          id
          title
          product {
            id
            title
          }
        }
      }
      moneyAmountsCount
      createdAt
      updatedAt
    }
    priceListsCount(where: $where)
  }
`;

const GET_PRICE_LIST = gql`
  query GetPriceList($id: ID!) {
    priceList(where: { id: $id }) {
      id
      name
      description
      type
      status
      startsAt
      endsAt
      customerGroups {
        id
        name
        usersCount
      }
      moneyAmounts {
        id
        amount
        currencyCode
        productVariant {
          id
          title
          product {
            id
            title
          }
        }
      }
      moneyAmountsCount
      createdAt
      updatedAt
    }
  }
`;

const GET_PRICE_LIST_STATUS_COUNTS = gql`
  query GetPriceListStatusCounts {
    allPriceListsCount: priceListsCount
    activePriceListsCount: priceListsCount(where: { status: { equals: "active" } })
    draftPriceListsCount: priceListsCount(where: { status: { equals: "draft" } })
    salePriceListsCount: priceListsCount(where: { type: { equals: "sale" } })
    overridePriceListsCount: priceListsCount(where: { type: { equals: "override" } })
  }
`;

// Server Actions
export async function getFilteredPriceLists({
  search = '',
  status,
  type,
  page = 1,
  pageSize = 20,
  sortBy = 'createdAt',
  sortOrder = 'desc'
}: {
  search?: string;
  status?: 'active' | 'draft';
  type?: 'sale' | 'override';
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  try {
    const skip = (page - 1) * pageSize;

    // Build where clause
    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } }
      ];
    }

    if (status) {
      where.status = { equals: status };
    }

    if (type) {
      where.type = { equals: type };
    }

    const orderBy = [{ [sortBy]: sortOrder }];

    const data = await keystoneClient(GET_FILTERED_PRICE_LISTS, {
      where,
      orderBy,
      take: pageSize,
      skip
    });

    return {
      success: true,
      data: {
        priceLists: data.priceLists as PriceListWithRelations[],
        totalCount: data.priceListsCount,
        page,
        pageSize,
        totalPages: Math.ceil(data.priceListsCount / pageSize)
      }
    };
  } catch (error) {
    console.error('Error fetching price lists:', error);
    return {
      success: false,
      error: 'Failed to fetch price lists'
    };
  }
}

export async function getPriceList(id: string) {
  try {
    const data = await keystoneClient(GET_PRICE_LIST, { id });

    return {
      success: true,
      data: {
        priceList: data.priceList as PriceListWithRelations
      }
    };
  } catch (error) {
    console.error('Error fetching price list:', error);
    return {
      success: false,
      error: 'Failed to fetch price list'
    };
  }
}

export async function getPriceListStatusCounts() {
  try {
    const data = await keystoneClient(GET_PRICE_LIST_STATUS_COUNTS);

    return {
      success: true,
      data: {
        all: data.allPriceListsCount,
        active: data.activePriceListsCount,
        draft: data.draftPriceListsCount,
        sale: data.salePriceListsCount,
        override: data.overridePriceListsCount
      }
    };
  } catch (error) {
    console.error('Error fetching price list status counts:', error);
    return {
      success: false,
      error: 'Failed to fetch price list status counts'
    };
  }
}

export async function updatePriceList(id: string, data: Partial<PriceListWithRelations>) {
  try {
    const UPDATE_PRICE_LIST = gql`
      mutation UpdatePriceList($id: ID!, $data: PriceListUpdateInput!) {
        updatePriceList(where: { id: $id }, data: $data) {
          id
          name
          description
          type
          status
        }
      }
    `;

    const result = await keystoneClient(UPDATE_PRICE_LIST, { id, data });

    return {
      success: true,
      data: result.updatePriceList
    };
  } catch (error) {
    console.error('Error updating price list:', error);
    return {
      success: false,
      error: 'Failed to update price list'
    };
  }
}

export async function deletePriceList(id: string) {
  try {
    const DELETE_PRICE_LIST = gql`
      mutation DeletePriceList($id: ID!) {
        deletePriceList(where: { id: $id }) {
          id
        }
      }
    `;

    await keystoneClient(DELETE_PRICE_LIST, { id });

    return {
      success: true
    };
  } catch (error) {
    console.error('Error deleting price list:', error);
    return {
      success: false,
      error: 'Failed to delete price list'
    };
  }
}
