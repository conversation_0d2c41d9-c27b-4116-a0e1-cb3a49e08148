import { NextResponse } from "next/server";
import { NextRequest } from "next/server";
import { keystoneClient } from "@/features/dashboard/lib/keystoneClient";

const basePath = "/dashboard";

// Lightweight check for redirectToInit status only
export async function checkInitStatus(request: NextRequest) {
  const query = `
    query redirectToInit {
      redirectToInit
    }
  `;

  const headers = {
    cookie: request.headers.get("cookie") || "",
  };

  const result = await keystoneClient(query, {}, { headers });
  
  if (result.success) {
    return result.data.redirectToInit;
  }
  
  return false;
}

// Get authenticated user from the request
export async function getAuthenticatedUser(request: NextRequest) {
  const query = `
    query authenticatedItem {
      authenticatedItem {
        ... on User {
          id
          role {
            canAccessDashboard
          }
        }
      }
      redirectToInit
    }
  `;

  // Pass cookie headers to keystoneClient
  const headers = {
    cookie: request.headers.get("cookie") || "",
  };

  const result = await keystoneClient(query, {}, { headers });
  
  if (result.success) {
    return {
      user: result.data.authenticatedItem,
      redirectToInit: result.data.redirectToInit
    };
  } else {
    console.error("Auth check failed:", result.error);
    return { user: null, redirectToInit: false };
  }
}

// Handles authentication and access control for dashboard routes
export async function handleDashboardRoutes(
  request: NextRequest, 
  user: any,
  redirectToInit: boolean
) {
  const pathname = request.nextUrl.pathname;
  
  // Only handle dashboard routes
  if (!pathname.startsWith(basePath)) {
    return null;
  }
  
  const isInitRoute = pathname.startsWith(`${basePath}/init`);
  const isSigninRoute = pathname.startsWith(`${basePath}/signin`);
  const isNoAccessRoute = pathname.startsWith(`${basePath}/no-access`);
  const isResetRoute = pathname.startsWith(`${basePath}/reset`);
  const fromPath = request.nextUrl.searchParams.get("from");

  // Handle redirectToInit for dashboard routes
  if (redirectToInit) {
    if (!isInitRoute) {
      return NextResponse.redirect(new URL(`${basePath}/init`, request.url));
    }
    return NextResponse.next();
  }

  // Prevent access to init page if not needed (when redirectToInit is false)
  if (isInitRoute && !redirectToInit) {
    return NextResponse.redirect(new URL(basePath, request.url));
  }

  // Handle unauthenticated users
  if (!user && !isSigninRoute && !isResetRoute) {
    const signinUrl = new URL(`${basePath}/signin`, request.url);
    if (!isNoAccessRoute) {
      signinUrl.searchParams.set("from", request.nextUrl.pathname);
    }
    return NextResponse.redirect(signinUrl);
  }

  // Handle authenticated users trying to access signin
  if (user && (isSigninRoute || isResetRoute)) {
    if (fromPath && !fromPath.includes("no-access")) {
      return NextResponse.redirect(new URL(fromPath, request.url));
    }
    return NextResponse.redirect(new URL(basePath, request.url));
  }

  // Check role permissions
  if (user && !isNoAccessRoute && !user.role?.canAccessDashboard) {
    return NextResponse.redirect(new URL(`${basePath}/no-access`, request.url));
  }

  return NextResponse.next();
} 