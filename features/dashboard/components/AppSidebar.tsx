'use client';

import * as React from 'react';
import {
  ChevronRight,
  LayoutDashboard,
  ArrowUpRight,
  Sparkles,
  Package,
  Users,
  Tag,
  Gift,
  BadgeDollarSign,
  Clipboard,
  BarChart3,
  LayoutList,
  ArrowLeftRight,
  ShieldCheck,
  Truck,
  Settings,
  Ticket,
} from 'lucide-react';
import { usePathname } from 'next/navigation';

import Link from 'next/link';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  useSidebar,
} from '@/components/ui/sidebar';

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Removed unused useTheme import
import { Logo, LogoIcon } from '@/components/Logo';
import { customNavItems, basePath } from '@/features/dashboard/lib/config';
import { LucideIcon } from 'lucide-react';
import { OnboardingCards } from '@/features/dashboard/components/OnboardingCards';
import OnboardingDialog from '@/features/dashboard/components/OnboardingDialog';
import {
  dismissOnboarding,
  startOnboarding,
} from '@/features/dashboard/actions/onboarding';
import { UserProfileClient } from '@/features/dashboard/components/UserProfileClient';
import type { User } from '@/features/dashboard/components/DashboardUI';

interface AppSidebarProps {
  sidebarLinks: Array<{ title: string; href: string }>;
  user?: User | null;
}

// Map icons to nav items based on title
const getIconForNavItem = (title: string): LucideIcon => {
  const iconMap: Record<string, LucideIcon> = {
    Orders: Ticket,
    Products: Package,
    Users: Users,
    Discounts: Tag,
    'Gift Cards': Gift,
    'Price Lists': BadgeDollarSign,
    'Draft Orders': Clipboard,
    Analytics: BarChart3,
    'Batch Jobs': LayoutList,
    Returns: ArrowLeftRight,
    Claims: ShieldCheck,
    Inventory: Package,
    Shipping: Truck,
    Settings: Settings,
    Onboarding: Sparkles,
  };

  return iconMap[title] || Package;
};

export function AppSidebar({ sidebarLinks = [], user }: AppSidebarProps) {
  const { isMobile, setOpenMobile } = useSidebar();
  const pathname = usePathname();
  const [isOnboardingDialogOpen, setIsOnboardingDialogOpen] =
    React.useState(false);

  // Filter sidebar links into platform and model links
  const platformLinks = React.useMemo(() => {
    return sidebarLinks.filter((link) =>
      customNavItems.some((item) => `${basePath}${item.href}` === link.href)
    );
  }, [sidebarLinks]);

  const modelLinks = React.useMemo(() => {
    return sidebarLinks.filter(
      (link) =>
        !customNavItems.some((item) => `${basePath}${item.href}` === link.href)
    );
  }, [sidebarLinks]);

  // Function to check if a link is active
  const isLinkActive = React.useCallback(
    (href: string) => {
      if (!pathname) return false;

      // Exact match for dashboard root
      if (
        href === `${basePath}/dashboard` &&
        pathname === `${basePath}/dashboard`
      ) {
        return true;
      }

      // For other pages, check if the pathname starts with the href
      // This handles nested routes like /dashboard/products/1
      if (href !== `${basePath}/dashboard`) {
        return pathname.startsWith(href);
      }

      return false;
    },
    [pathname]
  );

  // Dashboard items for the collapsible menu
  const dashboardItems = [
    {
      title: 'Platform',
      items: [
        // Show onboarding link when dismissed
        ...(user?.onboardingStatus === 'dismissed' ? [{
          title: 'Onboarding',
          href: '#onboarding',
        }] : []),
        ...platformLinks,
      ],
      isActive: true,
      icon: LayoutDashboard,
    },
    {
      title: 'Models',
      items: modelLinks,
      isActive: false,
      icon: Package,
    },
  ];

  // Note: We're not using a home link in the current implementation
  // but keeping the structure for future reference

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarMenuButton asChild>
          <div className="group-has-[[data-collapsible=icon]]/sidebar-wrapper:hidden p-2">
            <Logo />
          </div>
        </SidebarMenuButton>
        <SidebarMenuButton asChild>
          <div className="hidden group-has-[[data-collapsible=icon]]/sidebar-wrapper:block">
            <LogoIcon />
          </div>
        </SidebarMenuButton>
      </SidebarHeader>
      <SidebarContent className="no-scrollbar">
        {/* Main Navigation */}
        {/* <SidebarGroup>
          <SidebarGroupLabel>Platform</SidebarGroupLabel>
          <SidebarMenu>
            {mainNavItems.map((route) => (
              <SidebarMenuItem key={route.url}>
                <SidebarMenuButton asChild>
                  <Link href={route.url} onClick={() => setOpenMobile(false)}>
                    {route.icon && <route.icon className="h-4 w-4 stroke-2" />}
                    <span>{route.title}</span>
                  </Link>
                </SidebarMenuButton>
              </SidebarMenuItem>
            ))}
          </SidebarMenu>
        </SidebarGroup> */}

        {/* Dashboard Links - Collapsible/Dropdown */}
        {dashboardItems.map((dashboardItem) => (
          <SidebarGroup key={dashboardItem.title}>
            <SidebarGroupLabel>{dashboardItem.title}</SidebarGroupLabel>
            <div className="max-h-full overflow-y-auto group-has-[[data-collapsible=icon]]/sidebar-wrapper:hidden">
              <Collapsible
                key={dashboardItem.title}
                asChild
                defaultOpen={dashboardItem.isActive}
                className="group/collapsible"
              >
                <SidebarMenuItem>
                  <CollapsibleTrigger asChild>
                    <SidebarMenuButton>
                      <dashboardItem.icon className="h-4 w-4" />
                      <span>{dashboardItem.title}</span>
                      <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                    </SidebarMenuButton>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <SidebarMenuSub>
                      {dashboardItem.items.map((link) => {
                        // Get icon for platform links
                        const Icon =
                          dashboardItem.title === 'Platform'
                            ? getIconForNavItem(link.title)
                            : undefined;

                        const handleClick = (e: React.MouseEvent) => {
                          if (link.title === 'Onboarding') {
                            e.preventDefault();
                            setIsOnboardingDialogOpen(true);
                          }
                          setOpenMobile(false);
                        };

                        return (
                          <SidebarMenuSubItem key={link.href}>
                            <SidebarMenuSubButton
                              asChild
                              isActive={isLinkActive(link.href)}
                            >
                              <Link href={link.href} onClick={handleClick}>
                                {Icon && <Icon className="h-4 w-4 mr-2" />}
                                <span>{link.title}</span>
                              </Link>
                            </SidebarMenuSubButton>
                          </SidebarMenuSubItem>
                        );
                      })}
                    </SidebarMenuSub>
                  </CollapsibleContent>
                </SidebarMenuItem>
              </Collapsible>
            </div>

            <div className="hidden group-has-[[data-collapsible=icon]]/sidebar-wrapper:block">
              <DropdownMenu>
                <SidebarMenuItem>
                  <DropdownMenuTrigger asChild>
                    <SidebarMenuButton className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground">
                      <dashboardItem.icon className="h-4 w-4" />
                    </SidebarMenuButton>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent
                    side={isMobile ? 'bottom' : 'right'}
                    align={isMobile ? 'end' : 'start'}
                    className="min-w-56"
                  >
                    <div className="max-h-[calc(100vh-16rem)] overflow-y-auto py-1">
                      {dashboardItem.items.map((link) => {
                        // Get icon for platform links
                        const Icon =
                          dashboardItem.title === 'Platform'
                            ? getIconForNavItem(link.title)
                            : undefined;

                        const handleClick = (e: React.MouseEvent) => {
                          if (link.title === 'Onboarding') {
                            e.preventDefault();
                            setIsOnboardingDialogOpen(true);
                          }
                          setOpenMobile(false);
                        };

                        return (
                          <DropdownMenuItem
                            asChild
                            key={link.href}
                            className={
                              isLinkActive(link.href)
                                ? 'bg-blue-50 text-blue-600'
                                : ''
                            }
                          >
                            <Link href={link.href} onClick={handleClick}>
                              {Icon && <Icon className="h-4 w-4 mr-2" />}
                              <span>{link.title}</span>
                              {isLinkActive(link.href) && (
                                <div className="ml-auto h-2 w-2 rounded-full bg-blue-600" />
                              )}
                            </Link>
                          </DropdownMenuItem>
                        );
                      })}
                    </div>
                  </DropdownMenuContent>
                </SidebarMenuItem>
              </DropdownMenu>
            </div>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        {/* User Profile Section */}

        <SidebarMenu>
          {/* Onboarding Section */}
          <div className="w-full mb-2 overflow-visible">
            <OnboardingCards
              steps={[
                {
                  href: '#onboarding',
                  title: 'Welcome to Openfront',
                  description:
                    'Your store is empty. Click get started to configure your store with products, categories, and regions.',
                },
              ]}
              onboardingStatus={user?.onboardingStatus}
              userRole={user?.role}
              onDismiss={async () => {
                try {
                  const result = await dismissOnboarding();
                  if (!result.success) {
                    console.error('Error dismissing onboarding:', result.error);
                  }
                  // No need to reload the page - revalidatePath handles the update
                } catch (error) {
                  console.error('Error dismissing onboarding:', error);
                }
              }}
              onOpenDialog={() => setIsOnboardingDialogOpen(true)}
            />
          </div>
          {/* <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/api/graphql" target="_blank" rel="noopener noreferrer">
                <div className="text-fuchsia-500">
                  <svg
                    width="16"
                    height="16"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="M3.125 6.45c0-.224.12-.431.315-.542l6.25-3.572a.625.625 0 0 1 .62 0l6.25 3.572a.625.625 0 0 1 .315.542v7.099c0 .224-.12.431-.315.542l-6.25 3.572a.625.625 0 0 1-.62 0L3.44 14.09a.625.625 0 0 1-.315-.542V6.45ZM1.25 13.55a2.5 2.5 0 0 0 1.26 2.17l6.25 3.572a2.5 2.5 0 0 0 2.48 0l6.25-3.572a2.5 2.5 0 0 0 1.26-2.17V6.45a2.5 2.5 0 0 0-1.26-2.17L11.24.708a2.5 2.5 0 0 0-2.48 0L2.51 4.28a2.5 2.5 0 0 0-1.26 2.17v7.099Z"
                    />
                    <path
                      fillRule="evenodd"
                      clipRule="evenodd"
                      d="m10 .338-8.522 14.35h17.044L10 .337ZM4.772 12.812 10 4.01l5.228 8.802H4.772Z"
                    />
                  </svg>
                </div>
                <span>GraphQL Playground</span>
                <ArrowUpRight className="ml-auto h-4 w-4" />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <a href="/changelog" target="_blank" rel="noopener noreferrer">
                <Sparkles className="h-4 w-4 text-emerald-500" />
                <span>Changelog</span>
                <ArrowUpRight className="ml-auto h-4 w-4" />
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem> */}
        </SidebarMenu>
        {user && <UserProfileClient user={user} />}

      </SidebarFooter>
      <SidebarRail />
      <OnboardingDialog
        isOpen={isOnboardingDialogOpen}
        onClose={() => setIsOnboardingDialogOpen(false)}
      />
    </Sidebar>
  );
}
